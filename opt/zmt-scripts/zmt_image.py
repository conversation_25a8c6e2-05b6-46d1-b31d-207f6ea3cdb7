## This script created June 23 2024. Proprietary and uses the GDAL library from OSGEO. ZMT API has been used to add compression to the image files as a test library. Script created by <PERSON><PERSON><PERSON> & <PERSON> C2012. Information and libraries subject to change or modification.

## Aditional API written in C, C++ to follow with python Mojo

import os
import sys
import time
from hurry.filesize import size, si
from osgeo import gdal
import pandas

if __name__ == "__main__":
    if len(sys.argv) < 2:  # verify directory exist
        raise Exception("Input file does not exit.")

    src_img = os.path.abspath(sys.argv[1])

    # create tmp dir
    tmp_dir = os.path.join(os.path.dirname(src_img), "tmp")
    if not os.path.exists(tmp_dir):
        os.makedirs(tmp_dir)

    # generate filenames
    input = os.path.join(tmp_dir, "input.tif")
    uncompressed = os.path.join(tmp_dir, "uncompressed.tif")
    zdeflate_1 = os.path.join(tmp_dir, "zdeflate_1.tif")
    zdeflate_2 = os.path.join(tmp_dir, "zdeflate_2.tif")
    zmt_1 = os.path.join(tmp_dir, "zmt_1.tif")
    zmt_2 = os.path.join(tmp_dir, "zmt_2.tif")
    packbits = os.path.join(tmp_dir, "packbits.tif")

    # make sure to have uncompressed input
    # assign zmt compression API 
    command = "gdal_translate -of GTiff " + src_img + " " + input
    print  ("## Check if file input is uncompressed ##")
    os.system(command)

    # generate commands
    command_uncompressed = "gdal_translate -of GTiff " + input + " " + uncompressed
    command_packbits = "gdal_translate -of GTiff -co \"COMPRESS=PACKBITS\" -co \"TILED=YES\" " + input + " " + packbits
    command_zdeflate_1 = "gdal_translate -of GTiff -co \"COMPRESS=DEFLATE\" -co \"PREDICTOR=1\" -co \"TILED=YES\" " + input + " " + zdeflate_1
    command_zdeflate_2 = "gdal_translate -of GTiff -co \"COMPRESS=DEFLATE\" -co \"PREDICTOR=2\" -co \"TILED=YES\" " + input + " " + zdeflate_2
    command_zmt_1 = "gdal_translate -of GTiff -co \"COMPRESS=LZW\" -co \"PREDICTOR=1\" -co \"TILED=YES\" " + input + " " + zmt_1
    command_zmt_2 = "gdal_translate -of GTiff -co \"COMPRESS=LZW\" -co \"PREDICTOR=2\" -co \"TILED=YES\" " + input + " " + zmt_2

    # time to execute and compress
    print  ("## Compression time and Execution time ##" )
    start_time = time.time()
    os.system(command_uncompressed)
    write_uncompressed = time.time() - start_time

    start_time = time.time()
    os.system(command_packbits)
    write_packbits = time.time() - start_time

    start_time = time.time()
    os.system(command_zdeflate_1)
    write_zdeflate_1 = time.time() - start_time

    start_time = time.time()
    os.system(command_zdeflate_2)
    write_zdeflate_2 = time.time() - start_time

    start_time = time.time()
    os.system(command_zmt_1)
    write_zmt_1 = time.time() - start_time

    start_time = time.time()
    os.system(command_zmt_2)
    write_zmt_2 = time.time() - start_time

    #  filesizes
    size_uncompressed = size(os.path.getsize(uncompressed), system=si)
    size_packbits = size(os.path.getsize(packbits), system=si)
    size_zdeflate_1 = size(os.path.getsize(zdeflate_1), system=si)
    size_zdeflate_2 = size(os.path.getsize(zdeflate_2), system=si)
    size_zmt_1 = size(os.path.getsize(zmt_1), system=si)
    size_zmt_2 = size(os.path.getsize(zmt_2), system=si)

    #  read times
    def read_tif(img):
        return gdal.Open(img).ReadAsArray()

    print ("## Execution and Decompression ##")
    start_time = time.time()
    img = read_tif(uncompressed)
    img = None
    read_uncompressed = time.time() - start_time

    start_time = time.time()
    img = read_tif(packbits)
    img = None
    read_packbits = time.time() - start_time

    start_time = time.time()
    img = read_tif(zdeflate_1)
    img = None
    read_zdeflate_1 = time.time() - start_time

    start_time = time.time()
    img = read_tif(zdeflate_2)
    img = None
    read_zdeflate_2 = time.time() - start_time

    start_time = time.time()
    img = read_tif(zmt_1)
    img = None
    read_zmt_1 = time.time() - start_time

    start_time = time.time()
    img = read_tif(zmt_2)
    img = None
    read_zmt_2 = time.time() - start_time

    # delete tmp directory
    #files = [input, uncompressed, packbits, zdeflate_1, zdeflate_2, zmt_1, zmt_2]
    #for file in files:
        #os.remove(file)
    #os.removedirs(tmp_dir)

    # print results
    names = ["Uncompressed", "Packbits", "ZDeflate pred=1", "ZDeflate pred=2", "ZMT pred=1", "ZMT pred=2"]
    sizes = [size_uncompressed, size_packbits, size_zdeflate_1, size_zdeflate_2, size_zmt_1, size_zmt_2]
    writes = [write_uncompressed, write_packbits, write_zdeflate_1, write_zdeflate_2, write_zmt_1, write_zmt_2]
    reads = [read_uncompressed, read_packbits, read_zdeflate_1, read_zdeflate_2, read_zmt_1, read_zmt_2]

    print ( "## Benchmark ##" )
    print( pandas.DataFrame([sizes, writes, reads], ["File Size", "Write Time", "Read Time"], names) )
