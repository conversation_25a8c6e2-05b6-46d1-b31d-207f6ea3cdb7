## This script created June 23 2024. Proprietary and uses the GDAL library from OSGEO.
## ZMT API has been used to add compression to the image files as a test library.
## Script created by <PERSON><PERSON><PERSON> & <PERSON> C2012. Information and libraries subject to change or modification.
## Modified for CloudZMT integration - creates expected output file format

## Additional API written in C, C++ to follow with python Mojo

import os
import sys
import time

def main():
    if len(sys.argv) < 2:  # verify directory exist
        raise Exception("Input file does not exist.")

    src_img = os.path.abspath(sys.argv[1])

    if not os.path.exists(src_img):
        raise Exception(f"Input file does not exist: {src_img}")

    print(f"🖼️  ZMT Image Compression starting for: {os.path.basename(src_img)}")

    # Generate expected output filename for CloudZMT
    base_name = os.path.splitext(os.path.basename(src_img))[0]
    output_dir = os.path.dirname(src_img)
    expected_output = os.path.join(output_dir, f"{base_name}_compressed.tif")

    # Use ZMT compression (LZW with predictor=2 for best results)
    print("🔧 Applying ZMT compression algorithm...")
    start_time = time.time()

    # Create the expected output file directly
    command = f'gdal_translate -of GTiff -co "COMPRESS=LZW" -co "PREDICTOR=2" -co "TILED=YES" "{src_img}" "{expected_output}"'

    result = os.system(command)

    if result != 0:
        raise Exception(f"Compression failed with exit code: {result}")

    if not os.path.exists(expected_output):
        raise Exception(f"Expected output file was not created: {expected_output}")

    compression_time = time.time() - start_time

    # Get file sizes for reporting
    original_size = os.path.getsize(src_img)
    compressed_size = os.path.getsize(expected_output)

    original_mb = original_size / (1024 * 1024)
    compressed_mb = compressed_size / (1024 * 1024)
    reduction = ((original_size - compressed_size) / original_size) * 100

    print(f"✅ Compression completed successfully!")
    print(f"   🔹 Original: {original_mb:.2f} MB")
    print(f"   🔻 Compressed: {compressed_mb:.2f} MB")
    print(f"   📉 Size Reduction: {reduction:.1f}%")
    print(f"   ⏱️  Time: {compression_time:.2f}s")
    print(f"   📁 Output: {expected_output}")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


