#!/bin/bash

# Usage: ./compress_audio.sh [output_format]
# Example: ./compress_audio.sh mp3

OUTPUT_FORMAT="${1:-mp3}" # default to mp3 if not provided
SUPPORTED_FORMATS=("mp3" "aac" "opus" "m4a")

if [[ ! " ${SUPPORTED_FORMATS[*]} " =~ " ${OUTPUT_FORMAT} " ]]; then
  echo "❌ Unsupported format: $OUTPUT_FORMAT"
  echo "✅ Supported formats: ${SUPPORTED_FORMATS[*]}"
  exit 1
fi

echo "Compressing audio files to .$OUTPUT_FORMAT format, please wait..."
start_time=$SECONDS

mkdir -p encoded original

for f in *.{mp3,wav,aac,m4a,ogg}; do
  [[ -f "$f" ]] || continue

  file_start=$SECONDS
  base="${f%.*}"
  out="encoded/${base}_zmt.${OUTPUT_FORMAT}"

  original_size=$(stat -c %s "$f")

  echo "▶ Compressing: $f"

  case "$OUTPUT_FORMAT" in
    mp3)
      ffmpeg -y -hide_banner -loglevel error -i "$f" -codec:a libmp3lame -qscale:a 5 -ar 44100 -ac 2 "$out"
      ;;
    aac|m4a)
      ffmpeg -y -hide_banner -loglevel error -i "$f" -c:a aac -b:a 96k "$out"
      ;;
    opus)
      ffmpeg -y -hide_banner -loglevel error -i "$f" -c:a libopus -b:a 48k "$out"
      ;;
  esac

  cp "$f" "original/$f"

  compressed_size=$(stat -c %s "$out")
  file_elapsed=$(( SECONDS - file_start ))

  original_mb=$(awk "BEGIN {printf \"%.2f\", $original_size/1024/1024}")
  compressed_mb=$(awk "BEGIN {printf \"%.2f\", $compressed_size/1024/1024}")
  reduction=$(awk "BEGIN {printf \"%.1f\", (1 - $compressed_size / $original_size) * 100}")

  echo "✅ Done: $f → $out (${file_elapsed}s)"
  echo "   🔹 Original: ${original_mb} MB"
  echo "   🔻 Compressed: ${compressed_mb} MB"
  echo "   📉 Size Reduction: ${reduction}%"
done

elapsed=$(( SECONDS - start_time ))
echo "🎉 All files processed in ${elapsed}s"

