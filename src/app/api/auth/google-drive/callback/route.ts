import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const code = searchParams.get('code');
  const error = searchParams.get('error');

  if (error) {
    // Handle OAuth error
    return NextResponse.redirect(
      new URL(`/?error=${encodeURIComponent(error)}`, request.url)
    );
  }

  if (!code) {
    return NextResponse.redirect(
      new URL('/?error=no_code', request.url)
    );
  }

  // Return HTML that posts the code back to the parent window
  const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <title>Authentication Success</title>
      </head>
      <body>
        <script>
          // Send the authorization code to the parent window
          if (window.opener) {
            window.opener.postMessage({
              type: 'auth-success',
              provider: 'google-drive',
              code: '${code}'
            }, '*');
            window.close();
          } else {
            // Fallback: redirect to main app with code
            window.location.href = '/?code=${code}&provider=google-drive';
          }
        </script>
        <p>Authentication successful! This window should close automatically.</p>
      </body>
    </html>
  `;

  return new NextResponse(html, {
    headers: {
      'Content-Type': 'text/html',
    },
  });
}
