'use client';

import { CloudFile } from '@/lib/types';
import { getCompressionConfig } from '@/lib/compression-config';

interface CompressionPanelProps {
  selectedFiles: CloudFile[];
  onStartCompression: () => void;
  isProcessing: boolean;
}

export default function CompressionPanel({ 
  selectedFiles, 
  onStartCompression, 
  isProcessing 
}: CompressionPanelProps) {
  const formatFileSize = (bytes: number): string => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const totalSize = selectedFiles.reduce((sum, file) => sum + file.size, 0);

  // Check for large files that might cause issues
  const MAX_FILE_SIZE = 6 * 1024 * 1024 * 1024; // 4GB
  const largeFiles = selectedFiles.filter(file => file.size > MAX_FILE_SIZE);

  // Calculate file types breakdown
  const filesByType = selectedFiles.reduce((stats, file) => {
    const config = getCompressionConfig(file.name);
    if (config) {
      stats[config.description] = (stats[config.description] || 0) + 1;
    }
    return stats;
  }, {} as Record<string, number>);

  return (
    <div className="p-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Compression Settings</h2>
      
      {/* File Summary */}
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <h3 className="font-medium text-gray-900 mb-3">Selected Files Summary</h3>
        
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <p className="text-sm text-gray-600">Total Files</p>
            <p className="text-lg font-semibold text-gray-900">{selectedFiles.length}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Total Size</p>
            <p className="text-lg font-semibold text-gray-900">{formatFileSize(totalSize)}</p>
          </div>
        </div>

        {/* Large File Warning */}
        {largeFiles.length > 0 && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">Large File Warning</h3>
                <div className="mt-1 text-sm text-yellow-700">
                  <p>The following files exceed 6GB and cannot be compressed:</p>
                  <ul className="mt-1 list-disc list-inside">
                    {largeFiles.map(file => (
                      <li key={file.id}>{file.name} ({formatFileSize(file.size)})</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* File Types Breakdown */}
        <div className="mb-4">
          <p className="text-sm text-gray-600 mb-2">File Types</p>
          <div className="space-y-1">
            {Object.entries(filesByType).map(([type, count]) => (
              <div key={type} className="flex justify-between text-sm">
                <span className="text-gray-700">{type}</span>
                <span className="text-gray-900 font-medium">{count} file{count !== 1 ? 's' : ''}</span>
              </div>
            ))}
          </div>
        </div>
      </div>



      {/* Compression Options */}
      <div className="mb-6">
        <h3 className="font-medium text-gray-900 mb-3">Compression Options</h3>
        
        <div className="space-y-3">
          <div className="flex items-center">
            <input
              id="create-folder"
              type="checkbox"
              defaultChecked={true}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="create-folder" className="ml-2 text-sm text-gray-700">
              Create "Compressed" folder in cloud storage
            </label>
          </div>
          
          <div className="flex items-center">
            <input
              id="preserve-structure"
              type="checkbox"
              defaultChecked={true}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="preserve-structure" className="ml-2 text-sm text-gray-700">
              Preserve original file structure
            </label>
          </div>
          
          <div className="flex items-center">
            <input
              id="delete-originals"
              type="checkbox"
              defaultChecked={false}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="delete-originals" className="ml-2 text-sm text-gray-700">
              Delete original files after compression
            </label>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-600">
          Ready to compress {selectedFiles.length} file{selectedFiles.length !== 1 ? 's' : ''}
        </div>
        
        <button
          onClick={onStartCompression}
          disabled={isProcessing || selectedFiles.length === 0}
          className={`px-6 py-2 rounded-md font-medium transition-colors ${
            isProcessing || selectedFiles.length === 0
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {isProcessing ? (
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Processing...</span>
            </div>
          ) : (
            'Start Compression'
          )}
        </button>
      </div>

      {/* Warning for large files */}
      {totalSize > 100 * 1024 * 1024 && ( // 100MB
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">Large File Warning</h3>
              <p className="text-sm text-yellow-700 mt-1">
                You're compressing {formatFileSize(totalSize)} of data. This may take several minutes to complete.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
