'use client';

import { useState, useEffect } from 'react';
import { CloudFile } from '@/lib/types';
import { isFileSupported, getSupportedExtensions } from '@/lib/compression-config';

interface FileListProps {
  connectedProviders: string[];
  onFileSelect: (files: CloudFile[]) => void;
  selectedFiles: CloudFile[];
}

export default function FileList({ connectedProviders, onFileSelect, selectedFiles }: FileListProps) {
  const [files, setFiles] = useState<CloudFile[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [lastLoadedProvider, setLastLoadedProvider] = useState<string>('');
  const [filesCache, setFilesCache] = useState<Record<string, CloudFile[]>>({});

  useEffect(() => {
    if (selectedProvider && connectedProviders.includes(selectedProvider)) {
      // Check if we have cached files for this provider
      if (filesCache[selectedProvider]) {
        setFiles(filesCache[selectedProvider]);
        setLastLoadedProvider(selectedProvider);
      } else if (selectedProvider !== lastLoadedProvider) {
        // Only load files if provider changed or we haven't loaded for this provider yet
        loadFiles(selectedProvider);
      }
    }
  }, [selectedProvider, connectedProviders, lastLoadedProvider, filesCache]);

  const loadFiles = async (providerId: string) => {
    setLoading(true);
    setError('');

    try {
      // Get stored tokens
      const tokens = JSON.parse(localStorage.getItem('cloudTokens') || '{}');
      const providerTokens = tokens[providerId];

      if (!providerTokens) {
        throw new Error('No authentication tokens found');
      }

      const response = await fetch(`/api/files/${providerId}`, {
        headers: {
          'Authorization': `Bearer ${JSON.stringify(providerTokens)}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load files');
      }

      const { data } = await response.json();
      const loadedFiles = data.files || [];
      setFiles(loadedFiles);
      setLastLoadedProvider(providerId); // Track which provider we loaded

      // Cache the files for this provider
      setFilesCache(prev => ({
        ...prev,
        [providerId]: loadedFiles
      }));
    } catch (error) {
      console.error('Failed to load files:', error);
      setError(error instanceof Error ? error.message : 'Failed to load files');
    } finally {
      setLoading(false);
    }
  };

  const handleFileToggle = (file: CloudFile) => {
    const isSelected = selectedFiles.some(f => f.id === file.id);
    let newSelection: CloudFile[];
    
    if (isSelected) {
      newSelection = selectedFiles.filter(f => f.id !== file.id);
    } else {
      newSelection = [...selectedFiles, file];
    }
    
    onFileSelect(newSelection);
  };

  const handleSelectAll = () => {
    const supportedFiles = files.filter(file => isFileSupported(file.name));
    onFileSelect(supportedFiles);
  };

  const handleClearSelection = () => {
    onFileSelect([]);
  };

  const handleRefreshFiles = () => {
    if (selectedProvider) {
      // Clear cache for this provider to force fresh load
      setFilesCache(prev => {
        const newCache = { ...prev };
        delete newCache[selectedProvider];
        return newCache;
      });
      setLastLoadedProvider(''); // Reset to force reload
      loadFiles(selectedProvider);
    }
  };

  const formatFileSize = (bytes: number): string => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const supportedFiles = files.filter(file => isFileSupported(file.name));
  const supportedExtensions = getSupportedExtensions();

  if (connectedProviders.length === 0) {
    return (
      <div className="p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">File Selection</h2>
        <div className="text-center py-8">
          <div className="text-gray-400 mb-2">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-1">No Cloud Providers Connected</h3>
          <p className="text-gray-500">Connect to a cloud storage provider to browse and select files for compression.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-900">File Selection</h2>
        {selectedFiles.length > 0 && (
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">{selectedFiles.length} selected</span>
            <button
              onClick={handleClearSelection}
              className="text-sm text-red-600 hover:text-red-700"
            >
              Clear
            </button>
          </div>
        )}
      </div>

      {/* Provider Selection */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <label className="block text-sm font-medium text-gray-700">
            Select Cloud Provider
          </label>
          {selectedProvider && files.length > 0 && (
            <button
              onClick={handleRefreshFiles}
              disabled={loading}
              className="flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-700 disabled:text-gray-400"
            >
              <svg className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span>Refresh</span>
            </button>
          )}
        </div>
        <select
          value={selectedProvider}
          onChange={(e) => setSelectedProvider(e.target.value)}
          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">Choose a provider...</option>
          {connectedProviders.map(providerId => (
            <option key={providerId} value={providerId}>
              {providerId.charAt(0).toUpperCase() + providerId.slice(1).replace('-', ' ')}
            </option>
          ))}
        </select>
      </div>

      {/* Supported File Types Info */}
      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
        <h4 className="text-sm font-medium text-blue-900 mb-1">Supported File Types</h4>
        <p className="text-sm text-blue-700">
          {supportedExtensions.join(', ')}
        </p>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-500 mt-2">Loading files...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {/* File List */}
      {!loading && !error && files.length > 0 && (
        <div>
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm text-gray-600">
              {supportedFiles.length} of {files.length} files supported
            </span>
            {supportedFiles.length > 0 && (
              <button
                onClick={handleSelectAll}
                className="text-sm text-blue-600 hover:text-blue-700"
              >
                Select All Supported
              </button>
            )}
          </div>

          <div className="border border-gray-200 rounded-md divide-y divide-gray-200 max-h-96 overflow-y-auto">
            {files.map((file) => {
              const isSupported = isFileSupported(file.name);
              const isSelected = selectedFiles.some(f => f.id === file.id);
              
              return (
                <div
                  key={file.id}
                  className={`p-3 flex items-center justify-between ${
                    isSupported ? 'hover:bg-gray-50 cursor-pointer' : 'bg-gray-50 cursor-not-allowed opacity-60'
                  } ${isSelected ? 'bg-blue-50 border-blue-200' : ''}`}
                  onClick={() => isSupported && handleFileToggle(file)}
                >
                  <div className="flex items-center space-x-3">
                    {isSupported && (
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => handleFileToggle(file)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    )}
                    <div>
                      <p className="text-sm font-medium text-gray-900">{file.name}</p>
                      <p className="text-sm text-gray-500">
                        {formatFileSize(file.size)} • {file.extension}
                        {!isSupported && ' • Not supported'}
                      </p>
                    </div>
                  </div>
                  
                  <div className="text-sm text-gray-500">
                    {new Date(file.modifiedAt).toLocaleDateString()}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Empty State */}
      {!loading && !error && selectedProvider && files.length === 0 && (
        <div className="text-center py-8">
          <div className="text-gray-400 mb-2">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-1">No Files Found</h3>
          <p className="text-gray-500">This folder appears to be empty or you may not have access to view files.</p>
        </div>
      )}
    </div>
  );
}
