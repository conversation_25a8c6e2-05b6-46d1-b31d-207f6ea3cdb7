import { google } from 'googleapis';
import { CloudFile, AuthTokens } from '../types';
import { CloudStorageProvider } from './base';

export class GoogleDriveProvider extends CloudStorageProvider {
  private oauth2Client: any;
  private drive: any;

  constructor() {
    super('google-drive');
    this.initializeClient();
  }

  private initializeClient(): void {
    this.oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_REDIRECT_URI
    );

    this.drive = google.drive({ version: 'v3', auth: this.oauth2Client });
  }

  /**
   * Override setTokens to also update OAuth client credentials
   */
  setTokens(tokens: AuthTokens): void {
    super.setTokens(tokens);

    // Convert our token format to Google's format and set credentials
    const expiryDate = tokens.expiresAt
      ? (tokens.expiresAt instanceof Date ? tokens.expiresAt.getTime() : new Date(tokens.expiresAt).getTime())
      : undefined;

    this.oauth2Client.setCredentials({
      access_token: tokens.accessToken,
      refresh_token: tokens.refreshToken,
      expiry_date: expiryDate,
      scope: tokens.scope?.join(' ')
    });
  }

  getAuthUrl(): string {
    const scopes = [
      'https://www.googleapis.com/auth/drive.file',
      'https://www.googleapis.com/auth/drive.readonly',
      'https://www.googleapis.com/auth/userinfo.profile'
    ];

    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      prompt: 'consent'
    });
  }

  async authenticate(authCode: string): Promise<AuthTokens> {
    try {
      const { tokens } = await this.oauth2Client.getToken(authCode);
      
      const authTokens: AuthTokens = {
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token,
        expiresAt: tokens.expiry_date ? new Date(tokens.expiry_date) : undefined,
        scope: tokens.scope?.split(' ')
      };

      this.setTokens(authTokens);
      this.oauth2Client.setCredentials(tokens);

      return authTokens;
    } catch (error) {
      throw this.handleError(error, 'authentication');
    }
  }

  async refreshTokens(): Promise<AuthTokens> {
    try {
      if (!this.tokens?.refreshToken) {
        throw new Error('No refresh token available');
      }

      this.oauth2Client.setCredentials({
        refresh_token: this.tokens.refreshToken
      });

      const { credentials } = await this.oauth2Client.refreshAccessToken();
      
      const authTokens: AuthTokens = {
        accessToken: credentials.access_token!,
        refreshToken: credentials.refresh_token || this.tokens.refreshToken,
        expiresAt: credentials.expiry_date ? new Date(credentials.expiry_date) : undefined,
        scope: credentials.scope?.split(' ')
      };

      this.setTokens(authTokens);
      this.oauth2Client.setCredentials(credentials);

      return authTokens;
    } catch (error) {
      throw this.handleError(error, 'token refresh');
    }
  }

  async getUserInfo(): Promise<any> {
    try {
      const oauth2 = google.oauth2({ version: 'v2', auth: this.oauth2Client });
      const { data } = await oauth2.userinfo.get();
      
      return {
        id: data.id,
        name: data.name,
        email: data.email,
        avatar: data.picture
      };
    } catch (error) {
      throw this.handleError(error, 'get user info');
    }
  }

  async listFiles(folderId: string = 'root', pageToken?: string): Promise<{
    files: CloudFile[];
    nextPageToken?: string;
  }> {
    try {
      const response = await this.drive.files.list({
        q: `'${folderId}' in parents and trashed=false`,
        fields: 'nextPageToken, files(id, name, size, mimeType, createdTime, modifiedTime, thumbnailLink, webContentLink)',
        pageSize: 100,
        pageToken
      });

      const files: CloudFile[] = response.data.files.map((file: any) => ({
        id: file.id,
        name: file.name,
        size: parseInt(file.size) || 0,
        mimeType: file.mimeType,
        extension: this.getFileExtension(file.name),
        path: `/${file.name}`,
        provider: this.provider,
        downloadUrl: file.webContentLink,
        thumbnailUrl: file.thumbnailLink,
        createdAt: new Date(file.createdTime),
        modifiedAt: new Date(file.modifiedTime)
      }));

      return {
        files: files.filter(file => this.validateFile(file)),
        nextPageToken: response.data.nextPageToken
      };
    } catch (error) {
      throw this.handleError(error, 'list files');
    }
  }

  async downloadFile(fileId: string): Promise<Buffer> {
    try {
      // First, get file metadata to check MIME type
      const fileMetadata = await this.drive.files.get({
        fileId,
        fields: 'id, name, mimeType, size'
      });

      const file = fileMetadata.data;
      console.log(`Downloading file: ${file.name} (${file.mimeType})`);

      // Check if this is a Google Docs file that needs to be exported
      if (this.isGoogleDocsFile(file.mimeType)) {
        console.log(`File ${file.name} is a Google Docs file, using export instead of download`);
        return await this.exportGoogleDocsFile(fileId, file.mimeType, file.name);
      }

      // For regular binary files, check size and use appropriate download method
      console.log(`File ${file.name} is a binary file, using standard download`);
      const fileSize = parseInt(file.size || '0');

      // For files larger than 4GB, we need to handle them differently
      const MAX_BUFFER_SIZE = 4 * 1024 * 1024 * 1024; // 4GB
      if (fileSize > MAX_BUFFER_SIZE) {
        throw new Error(`File too large for in-memory processing: ${fileSize} bytes. Maximum supported size is ${MAX_BUFFER_SIZE} bytes (4GB). Consider implementing streaming download for files of this size.`);
      }

      const response = await this.drive.files.get({
        fileId,
        alt: 'media'
      }, {
        responseType: 'arraybuffer'
      });

      return Buffer.from(response.data);
    } catch (error) {
      throw this.handleError(error, 'download file');
    }
  }

  /**
   * Check if a file is a Google Docs file that requires export
   */
  private isGoogleDocsFile(mimeType: string): boolean {
    const googleDocsMimeTypes = [
      'application/vnd.google-apps.document',     // Google Docs
      'application/vnd.google-apps.spreadsheet', // Google Sheets
      'application/vnd.google-apps.presentation', // Google Slides
      'application/vnd.google-apps.drawing',      // Google Drawings
      'application/vnd.google-apps.form',         // Google Forms
      'application/vnd.google-apps.script',       // Google Apps Script
      'application/vnd.google-apps.site'          // Google Sites
    ];

    return googleDocsMimeTypes.includes(mimeType);
  }

  /**
   * Export Google Docs files to appropriate formats
   */
  private async exportGoogleDocsFile(fileId: string, mimeType: string, fileName: string): Promise<Buffer> {
    try {
      // Determine the best export format based on the Google Docs type
      const exportMimeType = this.getExportMimeType(mimeType, fileName);

      console.log(`Exporting ${fileName} from ${mimeType} to ${exportMimeType}`);

      const response = await this.drive.files.export({
        fileId,
        mimeType: exportMimeType
      }, {
        responseType: 'arraybuffer'
      });

      console.log(`Successfully exported ${fileName}, size: ${response.data.byteLength} bytes`);
      return Buffer.from(response.data);
    } catch (error) {
      console.error(`Failed to export Google Docs file ${fileName}:`, error);
      throw this.handleError(error, 'export Google Docs file');
    }
  }

  /**
   * Get the appropriate export MIME type for Google Docs files
   */
  private getExportMimeType(googleDocsMimeType: string, fileName: string): string {
    // Check if the filename suggests a specific format preference
    const fileExtension = this.getFileExtension(fileName).toLowerCase();

    switch (googleDocsMimeType) {
      case 'application/vnd.google-apps.document':
        // Google Docs - export as PDF by default, or Word if filename suggests it
        if (fileExtension === '.docx' || fileExtension === '.doc') {
          return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        }
        return 'application/pdf'; // Default to PDF for documents

      case 'application/vnd.google-apps.spreadsheet':
        // Google Sheets - export as Excel by default
        if (fileExtension === '.csv') {
          return 'text/csv';
        }
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

      case 'application/vnd.google-apps.presentation':
        // Google Slides - export as PowerPoint by default
        if (fileExtension === '.pdf') {
          return 'application/pdf';
        }
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';

      case 'application/vnd.google-apps.drawing':
        // Google Drawings - export as PDF by default
        if (fileExtension === '.png') {
          return 'image/png';
        } else if (fileExtension === '.jpg' || fileExtension === '.jpeg') {
          return 'image/jpeg';
        }
        return 'application/pdf';

      default:
        // For other Google Apps types, default to PDF
        return 'application/pdf';
    }
  }

  async uploadFile(filename: string, content: Buffer, folderId: string = 'root'): Promise<CloudFile> {
    try {
      // Convert Buffer to stream for Google Drive API
      const { Readable } = require('stream');
      const stream = new Readable();
      stream.push(content);
      stream.push(null); // End the stream

      const media = {
        mimeType: 'application/octet-stream',
        body: stream
      };

      const fileMetadata = {
        name: filename,
        parents: [folderId]
      };

      const response = await this.drive.files.create({
        resource: fileMetadata,
        media,
        fields: 'id, name, size, mimeType, createdTime, modifiedTime'
      });

      const file = response.data;
      return {
        id: file.id,
        name: file.name,
        size: parseInt(file.size) || content.length,
        mimeType: file.mimeType,
        extension: this.getFileExtension(file.name),
        path: `/${file.name}`,
        provider: this.provider,
        createdAt: new Date(file.createdTime),
        modifiedAt: new Date(file.modifiedTime)
      };
    } catch (error) {
      throw this.handleError(error, 'upload file');
    }
  }

  async createFolder(name: string, parentId: string = 'root'): Promise<string> {
    try {
      const fileMetadata = {
        name,
        mimeType: 'application/vnd.google-apps.folder',
        parents: [parentId]
      };

      const response = await this.drive.files.create({
        resource: fileMetadata,
        fields: 'id'
      });

      return response.data.id;
    } catch (error) {
      throw this.handleError(error, 'create folder');
    }
  }

  async deleteFile(fileId: string): Promise<void> {
    try {
      await this.drive.files.delete({ fileId });
    } catch (error) {
      throw this.handleError(error, 'delete file');
    }
  }

  async getDownloadUrl(fileId: string): Promise<string> {
    try {
      const response = await this.drive.files.get({
        fileId,
        fields: 'webContentLink'
      });

      return response.data.webContentLink;
    } catch (error) {
      throw this.handleError(error, 'get download URL');
    }
  }

  /**
   * Find or create the Compressed folder
   */
  async getOrCreateCompressedFolder(): Promise<string> {
    try {
      // First, try to find existing Compressed folder
      const response = await this.drive.files.list({
        q: "name='Compressed' and mimeType='application/vnd.google-apps.folder' and trashed=false",
        fields: 'files(id, name)'
      });

      if (response.data.files && response.data.files.length > 0) {
        return response.data.files[0].id;
      }

      // Create new Compressed folder if it doesn't exist
      return await this.createFolder('Compressed');
    } catch (error) {
      throw this.handleError(error, 'get or create compressed folder');
    }
  }
}
