import { spawn, ChildProcess } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { 
  CompressionJob, 
  CloudFile, 
  CompressionLog, 
  FileTypeConfig 
} from '../types';
import { 
  getCompressionConfig, 
  generateCompressionCommand, 
  getScriptPath,
  estimateCompressionRatio 
} from '../compression-config';
import { CloudStorageManager } from '../cloud-storage/factory';

export class CompressionEngine {
  private jobs: Map<string, CompressionJob> = new Map();
  private activeProcesses: Map<string, ChildProcess> = new Map();
  private storageManager: CloudStorageManager;
  private tempDir: string;

  constructor(storageManager: CloudStorageManager, tempDir: string = '/tmp/zmt-cloud') {
    this.storageManager = storageManager;
    this.tempDir = tempDir;
    this.ensureTempDir();
  }

  /**
   * Create a new compression job
   */
  async createJob(files: CloudFile[]): Promise<CompressionJob> {
    const jobId = uuidv4();
    const now = new Date();

    // Validate files
    const validFiles = files.filter(file => {
      const config = getCompressionConfig(file.name);
      return config !== null;
    });

    if (validFiles.length === 0) {
      throw new Error('No supported files found for compression');
    }

    const totalSize = validFiles.reduce((sum, file) => sum + file.size, 0);

    const job: CompressionJob = {
      id: jobId,
      files: validFiles,
      status: 'pending',
      progress: 0,
      startTime: now,
      originalSize: totalSize,
      logs: [],
      outputFiles: []
    };

    this.jobs.set(jobId, job);
    this.addLog(jobId, 'info', `Compression job created with ${validFiles.length} files`);

    return job;
  }

  /**
   * Start compression job
   */
  async startJob(jobId: string): Promise<void> {
    const job = this.jobs.get(jobId);
    if (!job) {
      throw new Error(`Job ${jobId} not found`);
    }

    if (job.status !== 'pending') {
      throw new Error(`Job ${jobId} is not in pending status`);
    }

    job.status = 'processing';
    job.startTime = new Date();
    this.addLog(jobId, 'info', 'Starting compression job');

    try {
      await this.processJob(job);
    } catch (error) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'Unknown error';
      job.endTime = new Date();
      this.addLog(jobId, 'error', `Job failed: ${job.error}`);
      throw error;
    }
  }

  /**
   * Process compression job
   */
  private async processJob(job: CompressionJob): Promise<void> {
    const jobDir = path.join(this.tempDir, job.id);
    await fs.mkdir(jobDir, { recursive: true });

    try {
      let processedFiles = 0;
      const totalFiles = job.files.length;

      for (const file of job.files) {
        this.addLog(job.id, 'info', `Processing file: ${file.name}`);
        
        try {
          const outputFile = await this.compressFile(file, jobDir, job.id);
          job.outputFiles.push(outputFile);
          
          processedFiles++;
          job.progress = Math.round((processedFiles / totalFiles) * 100);
          
          this.addLog(job.id, 'info', `Completed: ${file.name}`);
        } catch (error) {
          this.addLog(job.id, 'error', `Failed to compress ${file.name}: ${error}`);
          // Continue with other files even if one fails
        }
      }

      // Calculate final statistics
      job.compressedSize = job.outputFiles.reduce((sum, file) => sum + file.size, 0);
      job.compressionRatio = job.originalSize > 0 ? job.compressedSize / job.originalSize : 1;
      
      job.status = 'completed';
      job.endTime = new Date();
      job.duration = job.endTime.getTime() - job.startTime.getTime();
      
      this.addLog(job.id, 'info', 
        `Job completed. Original: ${this.formatBytes(job.originalSize)}, ` +
        `Compressed: ${this.formatBytes(job.compressedSize)}, ` +
        `Ratio: ${(job.compressionRatio * 100).toFixed(1)}%`
      );

    } finally {
      // Clean up temporary files
      await this.cleanupJobDir(jobDir);
    }
  }

  /**
   * Compress a single file
   */
  private async compressFile(file: CloudFile, jobDir: string, jobId: string): Promise<CloudFile> {
    console.log('=== Starting File Compression ===');
    console.log('File:', file);
    console.log('Job directory:', jobDir);

    const config = getCompressionConfig(file.name);
    if (!config) {
      throw new Error(`No compression config found for ${file.name}`);
    }
    console.log('Compression config:', config);

    // Download file from cloud storage
    const provider = this.storageManager.getConnectedProvider(file.provider);
    if (!provider) {
      throw new Error(`Provider ${file.provider} not connected`);
    }
    console.log('Provider found:', file.provider);

    const inputPath = path.join(jobDir, file.name);
    const outputPath = this.generateOutputPath(inputPath, config);
    console.log('Input path:', inputPath);
    console.log('Output path:', outputPath);

    // Download file
    console.log('Downloading file from cloud storage...');
    const fileContent = await provider.downloadFile(file.id);
    console.log('Downloaded file size:', fileContent.length, 'bytes');

    await fs.writeFile(inputPath, fileContent);
    console.log('File written to local storage');

    // Run compression
    console.log('Starting compression process...');
    this.addLog(jobId, 'info', `🚀 Starting compression for: ${file.name}`);
    await this.runCompressionScript(config, inputPath, outputPath, jobId);
    console.log('Compression completed');

    // Read compressed file
    console.log('Reading compressed file...');
    const compressedContent = await fs.readFile(outputPath);
    const compressedFilename = path.basename(outputPath);
    console.log('Compressed file size:', compressedContent.length, 'bytes');
    console.log('Compressed filename:', compressedFilename);

    // Upload compressed file back to cloud storage
    console.log('Uploading compressed file to cloud storage...');
    const compressedFolderId = await this.getCompressedFolderId(provider);
    console.log('Compressed folder ID:', compressedFolderId);

    const uploadedFile = await provider.uploadFile(
      compressedFilename,
      compressedContent,
      compressedFolderId
    );
    console.log('Upload completed:', uploadedFile);

    return uploadedFile;
  }

  /**
   * Run compression script
   */
  private async runCompressionScript(
    config: FileTypeConfig,
    inputPath: string,
    outputPath: string,
    jobId?: string
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const scriptPath = getScriptPath(config);
      const command = generateCompressionCommand(inputPath, outputPath, config);

      // Log detailed compression information
      console.log('=== Compression Script Execution ===');
      console.log('Config:', config);
      console.log('Script path:', scriptPath);
      console.log('Input path:', inputPath);
      console.log('Output path:', outputPath);
      console.log('Generated command:', command);
      console.log('Working directory:', path.dirname(inputPath));

      // Add this to the compression logs that show in the UI
      if (jobId) {
        this.addLog(jobId, 'info', `🔧 Executing command: ${command}`);
        this.addLog(jobId, 'info', `📁 Working directory: ${path.dirname(inputPath)}`);
      }

      // Parse command into executable and arguments
      // Need to handle file paths with spaces properly
      const parts = command.split(' ');
      const executable = parts[0];

      // For ZMT command: zmt a outputfile inputfile
      // We need to properly handle the file paths which may contain spaces
      let args: string[] = [];
      if (parts[1] === 'a') {
        // ZMT archive command: executable a outputfile inputfile
        args = [
          'a',
          outputPath, // Use the actual output path directly
          inputPath   // Use the actual input path directly
        ];
      } else {
        // For other commands, use the original parsing
        args = parts.slice(1);
      }

      console.log('Executable:', executable);
      console.log('Arguments:', args);

      // Check if executable exists
      const fs = require('fs');
      const executableExists = fs.existsSync(executable);
      console.log('Executable exists:', executableExists);

      if (!executableExists) {
        // Try absolute path from project root
        const projectRoot = process.cwd();
        const absoluteExecutable = path.resolve(projectRoot, executable);
        console.log('Trying absolute path:', absoluteExecutable);
        console.log('Absolute executable exists:', fs.existsSync(absoluteExecutable));
      }

      const process = spawn(executable, args, {
        cwd: path.dirname(inputPath),
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      process.stdout?.on('data', (data) => {
        stdout += data.toString();
      });

      process.stderr?.on('data', (data) => {
        stderr += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Compression failed with code ${code}: ${stderr}`));
        }
      });

      process.on('error', (error) => {
        reject(new Error(`Failed to start compression process: ${error.message}`));
      });

      // Set timeout for compression (30 minutes)
      setTimeout(() => {
        process.kill('SIGTERM');
        reject(new Error('Compression timeout'));
      }, 30 * 60 * 1000);
    });
  }

  /**
   * Generate output file path
   */
  private generateOutputPath(inputPath: string, config: FileTypeConfig): string {
    const dir = path.dirname(inputPath);
    const basename = path.basename(inputPath, path.extname(inputPath));
    
    // Different output extensions based on compression type
    let outputExt = '.zmt'; // Default ZMT extension
    
    if (config.script.includes('mp4')) {
      outputExt = '.mp4';
    } else if (config.script.includes('audio')) {
      outputExt = '.mp3';
    } else if (config.script.includes('image')) {
      outputExt = '.tif';
    }
    
    return path.join(dir, `${basename}_compressed${outputExt}`);
  }

  /**
   * Get or create compressed folder ID
   */
  private async getCompressedFolderId(provider: any): Promise<string> {
    if (typeof provider.getOrCreateCompressedFolder === 'function') {
      return await provider.getOrCreateCompressedFolder();
    }
    
    // Fallback: create folder in root
    return await provider.createFolder('Compressed');
  }

  /**
   * Add log entry to job
   */
  private addLog(jobId: string, level: 'info' | 'warning' | 'error', message: string): void {
    const job = this.jobs.get(jobId);
    if (!job) return;

    const log: CompressionLog = {
      id: uuidv4(),
      timestamp: new Date(),
      level,
      message
    };

    job.logs.push(log);
  }

  /**
   * Get job by ID
   */
  getJob(jobId: string): CompressionJob | null {
    return this.jobs.get(jobId) || null;
  }

  /**
   * Get all jobs
   */
  getAllJobs(): CompressionJob[] {
    return Array.from(this.jobs.values());
  }

  /**
   * Cancel job
   */
  async cancelJob(jobId: string): Promise<void> {
    const job = this.jobs.get(jobId);
    if (!job) {
      throw new Error(`Job ${jobId} not found`);
    }

    const process = this.activeProcesses.get(jobId);
    if (process) {
      process.kill('SIGTERM');
      this.activeProcesses.delete(jobId);
    }

    job.status = 'failed';
    job.error = 'Job cancelled by user';
    job.endTime = new Date();
    
    this.addLog(jobId, 'info', 'Job cancelled');
  }

  /**
   * Clean up job directory
   */
  private async cleanupJobDir(jobDir: string): Promise<void> {
    try {
      await fs.rm(jobDir, { recursive: true, force: true });
    } catch (error) {
      console.error('Failed to cleanup job directory:', error);
    }
  }

  /**
   * Ensure temp directory exists
   */
  private async ensureTempDir(): Promise<void> {
    try {
      await fs.mkdir(this.tempDir, { recursive: true });
    } catch (error) {
      console.error('Failed to create temp directory:', error);
    }
  }

  /**
   * Format bytes to human readable string
   */
  private formatBytes(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }
}
