import { FileTypeConfig } from './types';

// ZMT Compression configuration based on PRD
export const COMPRESSION_CONFIGS: Record<string, FileTypeConfig> = {
  // Documents and general files - ZMT compression
  documents: {
    extensions: ['.txt', '.pdf', '.xls', '.xlsx', '.doc', '.docx', '.psd', '.csv', '.db', '.dcm', '.ppt', '.pptx'],
    script: 'zmt',
    command: './opt/zmt-scripts/zmt a {outputFile} {inputFile}',
    description: 'ZMT compression for documents and general files'
  },

  // Video files - MP4 compression script
  video: {
    extensions: ['.mp4', '.mkv', '.3gp', '.avi', '.mov', '.webm'],
    script: 'compress_code_mp4_update.sh',
    command: './opt/zmt-scripts/compress_code_mp4_update.sh mp4',
    description: 'Video compression using MP4 optimization'
  },

  // Audio files - Audio compression script
  audio: {
    extensions: ['.mp3', '.aac', '.opus', '.m4a'],
    script: 'compress_code_audio.sh',
    command: './opt/zmt-scripts/compress_code_audio.sh mp3',
    description: 'Audio compression optimization'
  },

  // Y4M video files - Specialized video compression
  y4m: {
    extensions: ['.y4m'],
    script: 'compress_code_video.sh',
    command: './opt/zmt-scripts/compress_code_video.sh',
    description: 'Y4M video compression'
  },

  // TIFF images - Python image compression
  tiff: {
    extensions: ['.tif', '.tiff'],
    script: 'zmt_image.py',
    command: 'python3 ./opt/zmt-scripts/zmt_image.py {inputFile}',
    description: 'TIFF image compression using Python'
  }
};

/**
 * Get compression configuration for a file based on its extension
 */
export function getCompressionConfig(filename: string): FileTypeConfig | null {
  const extension = getFileExtension(filename);
  
  for (const config of Object.values(COMPRESSION_CONFIGS)) {
    if (config.extensions.includes(extension)) {
      return config;
    }
  }
  
  return null;
}

/**
 * Get file extension from filename
 */
export function getFileExtension(filename: string): string {
  const lastDotIndex = filename.lastIndexOf('.');
  if (lastDotIndex === -1) return '';
  return filename.substring(lastDotIndex).toLowerCase();
}

/**
 * Check if a file type is supported for compression
 */
export function isFileSupported(filename: string): boolean {
  return getCompressionConfig(filename) !== null;
}

/**
 * Get all supported file extensions
 */
export function getSupportedExtensions(): string[] {
  const extensions: string[] = [];
  Object.values(COMPRESSION_CONFIGS).forEach(config => {
    extensions.push(...config.extensions);
  });
  return [...new Set(extensions)].sort();
}

/**
 * Generate compression command for a specific file
 */
export function generateCompressionCommand(
  inputFile: string,
  outputFile: string,
  config: FileTypeConfig
): string {
  const path = require('path');
  const projectRoot = process.cwd();

  // Convert relative paths to absolute paths
  let command = config.command;

  // Replace script paths with absolute paths
  if (command.startsWith('./opt/zmt-scripts/')) {
    const scriptName = command.split('./opt/zmt-scripts/')[1].split(' ')[0];
    const absoluteScriptPath = path.resolve(projectRoot, 'opt', 'zmt-scripts', scriptName);
    command = command.replace(`./opt/zmt-scripts/${scriptName}`, absoluteScriptPath);
  }

  return command
    .replace('{inputFile}', inputFile)
    .replace('{outputFile}', outputFile);
}

/**
 * Get compression script path
 */
export function getScriptPath(config: FileTypeConfig): string {
  // In production, these would be absolute paths to the compression scripts
  return `/opt/zmt-scripts/${config.script}`;
}

/**
 * Estimate compression ratio based on file type
 */
export function estimateCompressionRatio(filename: string): number {
  const extension = getFileExtension(filename);
  
  // Estimated compression ratios based on file types
  const ratios: Record<string, number> = {
    // Documents - good compression
    '.txt': 0.3,
    '.pdf': 0.8,
    '.doc': 0.6,
    '.docx': 0.7,
    '.xls': 0.6,
    '.xlsx': 0.7,
    '.csv': 0.4,
    '.ppt': 0.7,
    '.pptx': 0.8,
    
    // Images - moderate compression
    '.tif': 0.5,
    '.tiff': 0.5,
    '.psd': 0.7,
    
    // Video - depends on quality settings
    '.mp4': 0.6,
    '.mkv': 0.5,
    '.avi': 0.4,
    '.mov': 0.6,
    '.webm': 0.7,
    '.3gp': 0.8,
    '.y4m': 0.3,
    
    // Audio - good compression
    '.mp3': 0.8, // Already compressed
    '.aac': 0.8, // Already compressed
    '.opus': 0.8, // Already compressed
    '.m4a': 0.8, // Already compressed
    
    // Database and other
    '.db': 0.5,
    '.dcm': 0.6
  };
  
  return ratios[extension] || 0.7; // Default 70% of original size
}


