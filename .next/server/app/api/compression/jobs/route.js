/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/compression/jobs/route";
exports.ids = ["app/api/compression/jobs/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcompression%2Fjobs%2Froute&page=%2Fapi%2Fcompression%2Fjobs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcompression%2Fjobs%2Froute.ts&appDir=%2Fhome%2Fcardinalvision%2FDocuments%2Faugment-projects%2Fzmt-cloud%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fcardinalvision%2FDocuments%2Faugment-projects%2Fzmt-cloud&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcompression%2Fjobs%2Froute&page=%2Fapi%2Fcompression%2Fjobs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcompression%2Fjobs%2Froute.ts&appDir=%2Fhome%2Fcardinalvision%2FDocuments%2Faugment-projects%2Fzmt-cloud%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fcardinalvision%2FDocuments%2Faugment-projects%2Fzmt-cloud&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_cardinalvision_Documents_augment_projects_zmt_cloud_src_app_api_compression_jobs_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/compression/jobs/route.ts */ \"(rsc)/./src/app/api/compression/jobs/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/compression/jobs/route\",\n        pathname: \"/api/compression/jobs\",\n        filename: \"route\",\n        bundlePath: \"app/api/compression/jobs/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/api/compression/jobs/route.ts\",\n    nextConfigOutput,\n    userland: _home_cardinalvision_Documents_augment_projects_zmt_cloud_src_app_api_compression_jobs_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcompression%2Fjobs%2Froute&page=%2Fapi%2Fcompression%2Fjobs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcompression%2Fjobs%2Froute.ts&appDir=%2Fhome%2Fcardinalvision%2FDocuments%2Faugment-projects%2Fzmt-cloud%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fcardinalvision%2FDocuments%2Faugment-projects%2Fzmt-cloud&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/compression/jobs/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/compression/jobs/route.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_compression_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/compression/engine */ \"(rsc)/./src/lib/compression/engine.ts\");\n/* harmony import */ var _lib_cloud_storage_factory__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/cloud-storage/factory */ \"(rsc)/./src/lib/cloud-storage/factory.ts\");\n\n\n\n// Global instances (in production, these would be properly managed)\nconst storageManager = new _lib_cloud_storage_factory__WEBPACK_IMPORTED_MODULE_2__.CloudStorageManager();\nconst compressionEngine = new _lib_compression_engine__WEBPACK_IMPORTED_MODULE_1__.CompressionEngine(storageManager);\nasync function POST(request) {\n    try {\n        const { files, tokens } = await request.json();\n        if (!files || !Array.isArray(files) || files.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Files array is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!tokens) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Authentication tokens are required'\n            }, {\n                status: 401\n            });\n        }\n        // Set up providers with tokens\n        for (const [providerId, providerTokens] of Object.entries(tokens)){\n            try {\n                // Get or create provider instance\n                let provider = storageManager.getConnectedProvider(providerId);\n                if (!provider) {\n                    // Create new provider instance\n                    provider = await storageManager.connectProvider(providerId);\n                }\n                if (provider) {\n                    // Set tokens and ensure provider is marked as connected\n                    provider.setTokens(providerTokens);\n                    // After setting tokens, the provider should be authenticated\n                    // Manually add to connected providers since setTokens makes it authenticated\n                    if (provider.isAuthenticated()) {\n                        storageManager['connectedProviders'].set(providerId, provider);\n                        console.log(`✅ Provider ${providerId} authenticated and connected`);\n                    } else {\n                        console.log(`❌ Provider ${providerId} failed to authenticate after setting tokens`);\n                    }\n                    console.log(`Provider connected status: ${storageManager.isProviderConnected(providerId)}`);\n                }\n            } catch (error) {\n                console.error(`Failed to set up provider ${providerId}:`, error);\n            }\n        }\n        // Create compression job\n        const job = await compressionEngine.createJob(files);\n        // Start job asynchronously\n        compressionEngine.startJob(job.id).catch((error)=>{\n            console.error('Job execution error:', error);\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: job\n        });\n    } catch (error) {\n        console.error('Create job error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to create compression job'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const jobId = searchParams.get('jobId');\n        if (jobId) {\n            // Get specific job\n            const job = compressionEngine.getJob(jobId);\n            if (!job) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Job not found'\n                }, {\n                    status: 404\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: job\n            });\n        } else {\n            // Get all jobs\n            const jobs = compressionEngine.getAllJobs();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: jobs\n            });\n        }\n    } catch (error) {\n        console.error('Get jobs error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to get jobs'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/compression/jobs/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/cloud-storage/base.ts":
/*!***************************************!*\
  !*** ./src/lib/cloud-storage/base.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CloudStorageProvider: () => (/* binding */ CloudStorageProvider)\n/* harmony export */ });\n/**\n * Base abstract class for cloud storage providers\n */ class CloudStorageProvider {\n    constructor(provider){\n        this.tokens = null;\n        this.provider = provider;\n    }\n    /**\n   * Set authentication tokens\n   */ setTokens(tokens) {\n        this.tokens = tokens;\n    }\n    /**\n   * Get authentication tokens\n   */ getTokens() {\n        return this.tokens;\n    }\n    /**\n   * Check if provider is authenticated\n   */ isAuthenticated() {\n        return this.tokens !== null && this.tokens.accessToken !== '';\n    }\n    /**\n   * Validate file before processing\n   */ validateFile(file) {\n        if (!file.id || !file.name) {\n            return false;\n        }\n        if (file.size <= 0) {\n            return false;\n        }\n        return true;\n    }\n    /**\n   * Convert file size to human readable format\n   */ formatFileSize(bytes) {\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB',\n            'TB'\n        ];\n        if (bytes === 0) return '0 Bytes';\n        const i = Math.floor(Math.log(bytes) / Math.log(1024));\n        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n    }\n    /**\n   * Extract file extension from filename\n   */ getFileExtension(filename) {\n        const lastDotIndex = filename.lastIndexOf('.');\n        if (lastDotIndex === -1) return '';\n        return filename.substring(lastDotIndex).toLowerCase();\n    }\n    /**\n   * Generate unique filename for compressed file\n   */ generateCompressedFilename(originalFilename) {\n        const extension = this.getFileExtension(originalFilename);\n        const nameWithoutExt = originalFilename.substring(0, originalFilename.lastIndexOf('.'));\n        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\n        return `${nameWithoutExt}_compressed_${timestamp}${extension}`;\n    }\n    /**\n   * Handle API errors consistently\n   */ handleError(error, operation) {\n        console.error(`${this.provider} ${operation} error:`, error);\n        if (error.response) {\n            const status = error.response.status;\n            const message = error.response.data?.error?.message || error.message;\n            switch(status){\n                case 401:\n                    return new Error(`Authentication failed: ${message}`);\n                case 403:\n                    return new Error(`Access denied: ${message}`);\n                case 404:\n                    return new Error(`Resource not found: ${message}`);\n                case 429:\n                    return new Error(`Rate limit exceeded: ${message}`);\n                case 500:\n                case 502:\n                case 503:\n                    return new Error(`Server error: ${message}`);\n                default:\n                    return new Error(`API error (${status}): ${message}`);\n            }\n        }\n        if (error.code) {\n            switch(error.code){\n                case 'ENOTFOUND':\n                    return new Error('Network error: Unable to connect to service');\n                case 'ETIMEDOUT':\n                    return new Error('Network error: Request timeout');\n                default:\n                    return new Error(`Network error: ${error.message}`);\n            }\n        }\n        return new Error(`${operation} failed: ${error.message || 'Unknown error'}`);\n    }\n    /**\n   * Retry mechanism for API calls\n   */ async retry(operation, maxRetries = 3, delay = 1000) {\n        let lastError;\n        for(let attempt = 1; attempt <= maxRetries; attempt++){\n            try {\n                return await operation();\n            } catch (error) {\n                lastError = error;\n                if (attempt === maxRetries) {\n                    throw lastError;\n                }\n                // Exponential backoff\n                const waitTime = delay * Math.pow(2, attempt - 1);\n                await new Promise((resolve)=>setTimeout(resolve, waitTime));\n            }\n        }\n        throw lastError;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/cloud-storage/base.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/cloud-storage/dropbox.ts":
/*!******************************************!*\
  !*** ./src/lib/cloud-storage/dropbox.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropboxProvider: () => (/* binding */ DropboxProvider)\n/* harmony export */ });\n/* harmony import */ var dropbox__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! dropbox */ \"dropbox\");\n/* harmony import */ var dropbox__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(dropbox__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _base__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base */ \"(rsc)/./src/lib/cloud-storage/base.ts\");\n\n\nclass DropboxProvider extends _base__WEBPACK_IMPORTED_MODULE_1__.CloudStorageProvider {\n    constructor(){\n        super('dropbox');\n        this.initializeClient();\n    }\n    initializeClient() {\n        this.dbx = new dropbox__WEBPACK_IMPORTED_MODULE_0__.Dropbox({\n            clientId: process.env.DROPBOX_CLIENT_ID,\n            clientSecret: process.env.DROPBOX_CLIENT_SECRET\n        });\n    }\n    getAuthUrl() {\n        const authUrl = this.dbx.getAuthenticationUrl(process.env.DROPBOX_REDIRECT_URI, undefined, 'code', 'offline');\n        return authUrl;\n    }\n    async authenticate(authCode) {\n        try {\n            const response = await this.dbx.getAccessTokenFromCode(process.env.DROPBOX_REDIRECT_URI, authCode);\n            const authTokens = {\n                accessToken: response.result.access_token,\n                refreshToken: response.result.refresh_token,\n                expiresAt: response.result.expires_in ? new Date(Date.now() + response.result.expires_in * 1000) : undefined\n            };\n            this.setTokens(authTokens);\n            this.dbx.setAccessToken(authTokens.accessToken);\n            return authTokens;\n        } catch (error) {\n            throw this.handleError(error, 'authentication');\n        }\n    }\n    async refreshTokens() {\n        try {\n            if (!this.tokens?.refreshToken) {\n                throw new Error('No refresh token available');\n            }\n            const response = await this.dbx.refreshAccessToken(this.tokens.refreshToken);\n            const authTokens = {\n                accessToken: response.result.access_token,\n                refreshToken: response.result.refresh_token || this.tokens.refreshToken,\n                expiresAt: response.result.expires_in ? new Date(Date.now() + response.result.expires_in * 1000) : undefined\n            };\n            this.setTokens(authTokens);\n            this.dbx.setAccessToken(authTokens.accessToken);\n            return authTokens;\n        } catch (error) {\n            throw this.handleError(error, 'token refresh');\n        }\n    }\n    async getUserInfo() {\n        try {\n            const response = await this.dbx.usersGetCurrentAccount();\n            const account = response.result;\n            return {\n                id: account.account_id,\n                name: account.name.display_name,\n                email: account.email,\n                avatar: account.profile_photo_url\n            };\n        } catch (error) {\n            throw this.handleError(error, 'get user info');\n        }\n    }\n    async listFiles(folderPath = '', cursor) {\n        try {\n            let response;\n            if (cursor) {\n                response = await this.dbx.filesListFolderContinue({\n                    cursor\n                });\n            } else {\n                response = await this.dbx.filesListFolder({\n                    path: folderPath || '',\n                    recursive: false,\n                    include_media_info: true,\n                    include_deleted: false\n                });\n            }\n            const files = response.result.entries.filter((entry)=>entry['.tag'] === 'file').map((file)=>({\n                    id: file.id,\n                    name: file.name,\n                    size: file.size,\n                    mimeType: this.getMimeTypeFromExtension(file.name),\n                    extension: this.getFileExtension(file.name),\n                    path: file.path_display,\n                    provider: this.provider,\n                    createdAt: new Date(file.client_modified),\n                    modifiedAt: new Date(file.server_modified)\n                }));\n            return {\n                files: files.filter((file)=>this.validateFile(file)),\n                nextPageToken: response.result.has_more ? response.result.cursor : undefined\n            };\n        } catch (error) {\n            throw this.handleError(error, 'list files');\n        }\n    }\n    async downloadFile(filePath) {\n        try {\n            const response = await this.dbx.filesDownload({\n                path: filePath\n            });\n            return Buffer.from(response.result.fileBinary);\n        } catch (error) {\n            throw this.handleError(error, 'download file');\n        }\n    }\n    async uploadFile(filename, content, folderPath = '') {\n        try {\n            const path = folderPath ? `${folderPath}/${filename}` : `/${filename}`;\n            const response = await this.dbx.filesUpload({\n                path,\n                contents: content,\n                mode: 'add',\n                autorename: true\n            });\n            const file = response.result;\n            return {\n                id: file.id,\n                name: file.name,\n                size: file.size,\n                mimeType: this.getMimeTypeFromExtension(file.name),\n                extension: this.getFileExtension(file.name),\n                path: file.path_display,\n                provider: this.provider,\n                createdAt: new Date(file.client_modified),\n                modifiedAt: new Date(file.server_modified)\n            };\n        } catch (error) {\n            throw this.handleError(error, 'upload file');\n        }\n    }\n    async createFolder(name, parentPath = '') {\n        try {\n            const path = parentPath ? `${parentPath}/${name}` : `/${name}`;\n            const response = await this.dbx.filesCreateFolderV2({\n                path,\n                autorename: false\n            });\n            return response.result.metadata.path_display;\n        } catch (error) {\n            throw this.handleError(error, 'create folder');\n        }\n    }\n    async deleteFile(filePath) {\n        try {\n            await this.dbx.filesDeleteV2({\n                path: filePath\n            });\n        } catch (error) {\n            throw this.handleError(error, 'delete file');\n        }\n    }\n    async getDownloadUrl(filePath) {\n        try {\n            const response = await this.dbx.filesGetTemporaryLink({\n                path: filePath\n            });\n            return response.result.link;\n        } catch (error) {\n            throw this.handleError(error, 'get download URL');\n        }\n    }\n    /**\n   * Find or create the Compressed folder\n   */ async getOrCreateCompressedFolder() {\n        try {\n            const compressedPath = '/Compressed';\n            try {\n                // Check if folder exists\n                await this.dbx.filesGetMetadata({\n                    path: compressedPath\n                });\n                return compressedPath;\n            } catch (error) {\n                if (error.status === 409) {\n                    // Folder doesn't exist, create it\n                    return await this.createFolder('Compressed');\n                }\n                throw error;\n            }\n        } catch (error) {\n            throw this.handleError(error, 'get or create compressed folder');\n        }\n    }\n    /**\n   * Get MIME type from file extension (Dropbox doesn't provide MIME types)\n   */ getMimeTypeFromExtension(filename) {\n        const extension = this.getFileExtension(filename);\n        const mimeTypes = {\n            '.txt': 'text/plain',\n            '.pdf': 'application/pdf',\n            '.doc': 'application/msword',\n            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n            '.xls': 'application/vnd.ms-excel',\n            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n            '.ppt': 'application/vnd.ms-powerpoint',\n            '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',\n            '.csv': 'text/csv',\n            '.mp4': 'video/mp4',\n            '.mkv': 'video/x-matroska',\n            '.avi': 'video/x-msvideo',\n            '.mov': 'video/quicktime',\n            '.webm': 'video/webm',\n            '.mp3': 'audio/mpeg',\n            '.aac': 'audio/aac',\n            '.opus': 'audio/opus',\n            '.m4a': 'audio/mp4',\n            '.tif': 'image/tiff',\n            '.tiff': 'image/tiff',\n            '.y4m': 'video/x-yuv4mpeg',\n            '.psd': 'image/vnd.adobe.photoshop',\n            '.db': 'application/x-sqlite3',\n            '.dcm': 'application/dicom'\n        };\n        return mimeTypes[extension] || 'application/octet-stream';\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/cloud-storage/dropbox.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/cloud-storage/factory.ts":
/*!******************************************!*\
  !*** ./src/lib/cloud-storage/factory.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CloudStorageFactory: () => (/* binding */ CloudStorageFactory),\n/* harmony export */   CloudStorageManager: () => (/* binding */ CloudStorageManager)\n/* harmony export */ });\n/* harmony import */ var _google_drive__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./google-drive */ \"(rsc)/./src/lib/cloud-storage/google-drive.ts\");\n/* harmony import */ var _dropbox__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./dropbox */ \"(rsc)/./src/lib/cloud-storage/dropbox.ts\");\n\n\n/**\n * Factory class for creating cloud storage providers\n */ class CloudStorageFactory {\n    static{\n        this.providers = new Map();\n    }\n    /**\n   * Get or create a cloud storage provider instance\n   */ static getProvider(providerId) {\n        if (!this.providers.has(providerId)) {\n            const provider = this.createProvider(providerId);\n            this.providers.set(providerId, provider);\n        }\n        return this.providers.get(providerId);\n    }\n    /**\n   * Create a new provider instance\n   */ static createProvider(providerId) {\n        switch(providerId){\n            case 'google-drive':\n                return new _google_drive__WEBPACK_IMPORTED_MODULE_0__.GoogleDriveProvider();\n            case 'dropbox':\n                return new _dropbox__WEBPACK_IMPORTED_MODULE_1__.DropboxProvider();\n            case 'icloud':\n                // iCloud implementation would go here\n                // For now, throw an error as it's not implemented\n                throw new Error('iCloud provider not yet implemented');\n            default:\n                throw new Error(`Unsupported cloud provider: ${providerId}`);\n        }\n    }\n    /**\n   * Get all available providers\n   */ static getAvailableProviders() {\n        return [\n            {\n                id: 'google-drive',\n                name: 'Google Drive',\n                icon: '/icons/google-drive.svg',\n                isConnected: false\n            },\n            {\n                id: 'dropbox',\n                name: 'Dropbox',\n                icon: '/icons/dropbox.svg',\n                isConnected: false\n            },\n            {\n                id: 'icloud',\n                name: 'iCloud Files',\n                icon: '/icons/icloud.svg',\n                isConnected: false\n            }\n        ];\n    }\n    /**\n   * Check if a provider is supported\n   */ static isProviderSupported(providerId) {\n        return [\n            'google-drive',\n            'dropbox',\n            'icloud'\n        ].includes(providerId);\n    }\n    /**\n   * Clear all provider instances (useful for testing)\n   */ static clearProviders() {\n        this.providers.clear();\n    }\n    /**\n   * Get provider configuration requirements\n   */ static getProviderConfig(providerId) {\n        switch(providerId){\n            case 'google-drive':\n                return {\n                    requiredEnvVars: [\n                        'GOOGLE_CLIENT_ID',\n                        'GOOGLE_CLIENT_SECRET',\n                        'GOOGLE_REDIRECT_URI'\n                    ],\n                    scopes: [\n                        'https://www.googleapis.com/auth/drive.file',\n                        'https://www.googleapis.com/auth/drive.readonly',\n                        'https://www.googleapis.com/auth/userinfo.profile'\n                    ],\n                    authType: 'oauth2'\n                };\n            case 'dropbox':\n                return {\n                    requiredEnvVars: [\n                        'DROPBOX_CLIENT_ID',\n                        'DROPBOX_CLIENT_SECRET',\n                        'DROPBOX_REDIRECT_URI'\n                    ],\n                    scopes: [\n                        'files.content.write',\n                        'files.content.read',\n                        'files.metadata.read'\n                    ],\n                    authType: 'oauth2'\n                };\n            case 'icloud':\n                return {\n                    requiredEnvVars: [\n                        'ICLOUD_CLIENT_ID',\n                        'ICLOUD_CLIENT_SECRET',\n                        'ICLOUD_REDIRECT_URI'\n                    ],\n                    scopes: [\n                        'cloudkit'\n                    ],\n                    authType: 'oauth2'\n                };\n            default:\n                throw new Error(`Unknown provider: ${providerId}`);\n        }\n    }\n    /**\n   * Validate provider configuration\n   */ static validateProviderConfig(providerId) {\n        const config = this.getProviderConfig(providerId);\n        const missingVars = [];\n        for (const envVar of config.requiredEnvVars){\n            if (!process.env[envVar]) {\n                missingVars.push(envVar);\n            }\n        }\n        return {\n            isValid: missingVars.length === 0,\n            missingVars\n        };\n    }\n}\n/**\n * Cloud Storage Manager - High-level interface for managing multiple providers\n */ class CloudStorageManager {\n    /**\n   * Connect to a cloud provider\n   */ async connectProvider(providerId, authCode) {\n        const provider = CloudStorageFactory.getProvider(providerId);\n        if (authCode) {\n            await provider.authenticate(authCode);\n        }\n        if (provider.isAuthenticated()) {\n            this.connectedProviders.set(providerId, provider);\n        }\n        return provider;\n    }\n    /**\n   * Disconnect from a cloud provider\n   */ disconnectProvider(providerId) {\n        this.connectedProviders.delete(providerId);\n    }\n    /**\n   * Get a connected provider\n   */ getConnectedProvider(providerId) {\n        return this.connectedProviders.get(providerId) || null;\n    }\n    /**\n   * Get all connected providers\n   */ getConnectedProviders() {\n        return Array.from(this.connectedProviders.keys());\n    }\n    /**\n   * Check if a provider is connected\n   */ isProviderConnected(providerId) {\n        const provider = this.connectedProviders.get(providerId);\n        return provider ? provider.isAuthenticated() : false;\n    }\n    /**\n   * Get provider status for all available providers\n   */ getProviderStatus() {\n        const providers = CloudStorageFactory.getAvailableProviders();\n        return providers.map((provider)=>({\n                ...provider,\n                isConnected: this.isProviderConnected(provider.id)\n            }));\n    }\n    /**\n   * Refresh tokens for all connected providers\n   */ async refreshAllTokens() {\n        const refreshPromises = Array.from(this.connectedProviders.values()).map((provider)=>provider.refreshTokens().catch((error)=>{\n                console.error('Failed to refresh tokens:', error);\n            }));\n        await Promise.all(refreshPromises);\n    }\n    constructor(){\n        this.connectedProviders = new Map();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/cloud-storage/factory.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/cloud-storage/google-drive.ts":
/*!***********************************************!*\
  !*** ./src/lib/cloud-storage/google-drive.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GoogleDriveProvider: () => (/* binding */ GoogleDriveProvider)\n/* harmony export */ });\n/* harmony import */ var googleapis__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! googleapis */ \"googleapis\");\n/* harmony import */ var googleapis__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(googleapis__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _base__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base */ \"(rsc)/./src/lib/cloud-storage/base.ts\");\n\n\nclass GoogleDriveProvider extends _base__WEBPACK_IMPORTED_MODULE_1__.CloudStorageProvider {\n    constructor(){\n        super('google-drive');\n        this.initializeClient();\n    }\n    initializeClient() {\n        this.oauth2Client = new googleapis__WEBPACK_IMPORTED_MODULE_0__.google.auth.OAuth2(process.env.GOOGLE_CLIENT_ID, process.env.GOOGLE_CLIENT_SECRET, process.env.GOOGLE_REDIRECT_URI);\n        this.drive = googleapis__WEBPACK_IMPORTED_MODULE_0__.google.drive({\n            version: 'v3',\n            auth: this.oauth2Client\n        });\n    }\n    /**\n   * Override setTokens to also update OAuth client credentials\n   */ setTokens(tokens) {\n        super.setTokens(tokens);\n        // Convert our token format to Google's format and set credentials\n        const expiryDate = tokens.expiresAt ? tokens.expiresAt instanceof Date ? tokens.expiresAt.getTime() : new Date(tokens.expiresAt).getTime() : undefined;\n        this.oauth2Client.setCredentials({\n            access_token: tokens.accessToken,\n            refresh_token: tokens.refreshToken,\n            expiry_date: expiryDate,\n            scope: tokens.scope?.join(' ')\n        });\n    }\n    getAuthUrl() {\n        const scopes = [\n            'https://www.googleapis.com/auth/drive.file',\n            'https://www.googleapis.com/auth/drive.readonly',\n            'https://www.googleapis.com/auth/userinfo.profile'\n        ];\n        return this.oauth2Client.generateAuthUrl({\n            access_type: 'offline',\n            scope: scopes,\n            prompt: 'consent'\n        });\n    }\n    async authenticate(authCode) {\n        try {\n            const { tokens } = await this.oauth2Client.getToken(authCode);\n            const authTokens = {\n                accessToken: tokens.access_token,\n                refreshToken: tokens.refresh_token,\n                expiresAt: tokens.expiry_date ? new Date(tokens.expiry_date) : undefined,\n                scope: tokens.scope?.split(' ')\n            };\n            this.setTokens(authTokens);\n            this.oauth2Client.setCredentials(tokens);\n            return authTokens;\n        } catch (error) {\n            throw this.handleError(error, 'authentication');\n        }\n    }\n    async refreshTokens() {\n        try {\n            if (!this.tokens?.refreshToken) {\n                throw new Error('No refresh token available');\n            }\n            this.oauth2Client.setCredentials({\n                refresh_token: this.tokens.refreshToken\n            });\n            const { credentials } = await this.oauth2Client.refreshAccessToken();\n            const authTokens = {\n                accessToken: credentials.access_token,\n                refreshToken: credentials.refresh_token || this.tokens.refreshToken,\n                expiresAt: credentials.expiry_date ? new Date(credentials.expiry_date) : undefined,\n                scope: credentials.scope?.split(' ')\n            };\n            this.setTokens(authTokens);\n            this.oauth2Client.setCredentials(credentials);\n            return authTokens;\n        } catch (error) {\n            throw this.handleError(error, 'token refresh');\n        }\n    }\n    async getUserInfo() {\n        try {\n            const oauth2 = googleapis__WEBPACK_IMPORTED_MODULE_0__.google.oauth2({\n                version: 'v2',\n                auth: this.oauth2Client\n            });\n            const { data } = await oauth2.userinfo.get();\n            return {\n                id: data.id,\n                name: data.name,\n                email: data.email,\n                avatar: data.picture\n            };\n        } catch (error) {\n            throw this.handleError(error, 'get user info');\n        }\n    }\n    async listFiles(folderId = 'root', pageToken) {\n        try {\n            const response = await this.drive.files.list({\n                q: `'${folderId}' in parents and trashed=false`,\n                fields: 'nextPageToken, files(id, name, size, mimeType, createdTime, modifiedTime, thumbnailLink, webContentLink)',\n                pageSize: 100,\n                pageToken\n            });\n            const files = response.data.files.map((file)=>({\n                    id: file.id,\n                    name: file.name,\n                    size: parseInt(file.size) || 0,\n                    mimeType: file.mimeType,\n                    extension: this.getFileExtension(file.name),\n                    path: `/${file.name}`,\n                    provider: this.provider,\n                    downloadUrl: file.webContentLink,\n                    thumbnailUrl: file.thumbnailLink,\n                    createdAt: new Date(file.createdTime),\n                    modifiedAt: new Date(file.modifiedTime)\n                }));\n            return {\n                files: files.filter((file)=>this.validateFile(file)),\n                nextPageToken: response.data.nextPageToken\n            };\n        } catch (error) {\n            throw this.handleError(error, 'list files');\n        }\n    }\n    async downloadFile(fileId) {\n        try {\n            const response = await this.drive.files.get({\n                fileId,\n                alt: 'media'\n            }, {\n                responseType: 'arraybuffer'\n            });\n            return Buffer.from(response.data);\n        } catch (error) {\n            throw this.handleError(error, 'download file');\n        }\n    }\n    async uploadFile(filename, content, folderId = 'root') {\n        try {\n            // Convert Buffer to stream for Google Drive API\n            const { Readable } = __webpack_require__(/*! stream */ \"stream\");\n            const stream = new Readable();\n            stream.push(content);\n            stream.push(null); // End the stream\n            const media = {\n                mimeType: 'application/octet-stream',\n                body: stream\n            };\n            const fileMetadata = {\n                name: filename,\n                parents: [\n                    folderId\n                ]\n            };\n            const response = await this.drive.files.create({\n                resource: fileMetadata,\n                media,\n                fields: 'id, name, size, mimeType, createdTime, modifiedTime'\n            });\n            const file = response.data;\n            return {\n                id: file.id,\n                name: file.name,\n                size: parseInt(file.size) || content.length,\n                mimeType: file.mimeType,\n                extension: this.getFileExtension(file.name),\n                path: `/${file.name}`,\n                provider: this.provider,\n                createdAt: new Date(file.createdTime),\n                modifiedAt: new Date(file.modifiedTime)\n            };\n        } catch (error) {\n            throw this.handleError(error, 'upload file');\n        }\n    }\n    async createFolder(name, parentId = 'root') {\n        try {\n            const fileMetadata = {\n                name,\n                mimeType: 'application/vnd.google-apps.folder',\n                parents: [\n                    parentId\n                ]\n            };\n            const response = await this.drive.files.create({\n                resource: fileMetadata,\n                fields: 'id'\n            });\n            return response.data.id;\n        } catch (error) {\n            throw this.handleError(error, 'create folder');\n        }\n    }\n    async deleteFile(fileId) {\n        try {\n            await this.drive.files.delete({\n                fileId\n            });\n        } catch (error) {\n            throw this.handleError(error, 'delete file');\n        }\n    }\n    async getDownloadUrl(fileId) {\n        try {\n            const response = await this.drive.files.get({\n                fileId,\n                fields: 'webContentLink'\n            });\n            return response.data.webContentLink;\n        } catch (error) {\n            throw this.handleError(error, 'get download URL');\n        }\n    }\n    /**\n   * Find or create the Compressed folder\n   */ async getOrCreateCompressedFolder() {\n        try {\n            // First, try to find existing Compressed folder\n            const response = await this.drive.files.list({\n                q: \"name='Compressed' and mimeType='application/vnd.google-apps.folder' and trashed=false\",\n                fields: 'files(id, name)'\n            });\n            if (response.data.files && response.data.files.length > 0) {\n                return response.data.files[0].id;\n            }\n            // Create new Compressed folder if it doesn't exist\n            return await this.createFolder('Compressed');\n        } catch (error) {\n            throw this.handleError(error, 'get or create compressed folder');\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2Nsb3VkLXN0b3JhZ2UvZ29vZ2xlLWRyaXZlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBb0M7QUFFVTtBQUV2QyxNQUFNRSw0QkFBNEJELHVEQUFvQkE7SUFJM0RFLGFBQWM7UUFDWixLQUFLLENBQUM7UUFDTixJQUFJLENBQUNDLGdCQUFnQjtJQUN2QjtJQUVRQSxtQkFBeUI7UUFDL0IsSUFBSSxDQUFDQyxZQUFZLEdBQUcsSUFBSUwsOENBQU1BLENBQUNNLElBQUksQ0FBQ0MsTUFBTSxDQUN4Q0MsUUFBUUMsR0FBRyxDQUFDQyxnQkFBZ0IsRUFDNUJGLFFBQVFDLEdBQUcsQ0FBQ0Usb0JBQW9CLEVBQ2hDSCxRQUFRQyxHQUFHLENBQUNHLG1CQUFtQjtRQUdqQyxJQUFJLENBQUNDLEtBQUssR0FBR2IsOENBQU1BLENBQUNhLEtBQUssQ0FBQztZQUFFQyxTQUFTO1lBQU1SLE1BQU0sSUFBSSxDQUFDRCxZQUFZO1FBQUM7SUFDckU7SUFFQTs7R0FFQyxHQUNEVSxVQUFVQyxNQUFrQixFQUFRO1FBQ2xDLEtBQUssQ0FBQ0QsVUFBVUM7UUFFaEIsa0VBQWtFO1FBQ2xFLE1BQU1DLGFBQWFELE9BQU9FLFNBQVMsR0FDOUJGLE9BQU9FLFNBQVMsWUFBWUMsT0FBT0gsT0FBT0UsU0FBUyxDQUFDRSxPQUFPLEtBQUssSUFBSUQsS0FBS0gsT0FBT0UsU0FBUyxFQUFFRSxPQUFPLEtBQ25HQztRQUVKLElBQUksQ0FBQ2hCLFlBQVksQ0FBQ2lCLGNBQWMsQ0FBQztZQUMvQkMsY0FBY1AsT0FBT1EsV0FBVztZQUNoQ0MsZUFBZVQsT0FBT1UsWUFBWTtZQUNsQ0MsYUFBYVY7WUFDYlcsT0FBT1osT0FBT1ksS0FBSyxFQUFFQyxLQUFLO1FBQzVCO0lBQ0Y7SUFFQUMsYUFBcUI7UUFDbkIsTUFBTUMsU0FBUztZQUNiO1lBQ0E7WUFDQTtTQUNEO1FBRUQsT0FBTyxJQUFJLENBQUMxQixZQUFZLENBQUMyQixlQUFlLENBQUM7WUFDdkNDLGFBQWE7WUFDYkwsT0FBT0c7WUFDUEcsUUFBUTtRQUNWO0lBQ0Y7SUFFQSxNQUFNQyxhQUFhQyxRQUFnQixFQUF1QjtRQUN4RCxJQUFJO1lBQ0YsTUFBTSxFQUFFcEIsTUFBTSxFQUFFLEdBQUcsTUFBTSxJQUFJLENBQUNYLFlBQVksQ0FBQ2dDLFFBQVEsQ0FBQ0Q7WUFFcEQsTUFBTUUsYUFBeUI7Z0JBQzdCZCxhQUFhUixPQUFPTyxZQUFZO2dCQUNoQ0csY0FBY1YsT0FBT1MsYUFBYTtnQkFDbENQLFdBQVdGLE9BQU9XLFdBQVcsR0FBRyxJQUFJUixLQUFLSCxPQUFPVyxXQUFXLElBQUlOO2dCQUMvRE8sT0FBT1osT0FBT1ksS0FBSyxFQUFFVyxNQUFNO1lBQzdCO1lBRUEsSUFBSSxDQUFDeEIsU0FBUyxDQUFDdUI7WUFDZixJQUFJLENBQUNqQyxZQUFZLENBQUNpQixjQUFjLENBQUNOO1lBRWpDLE9BQU9zQjtRQUNULEVBQUUsT0FBT0UsT0FBTztZQUNkLE1BQU0sSUFBSSxDQUFDQyxXQUFXLENBQUNELE9BQU87UUFDaEM7SUFDRjtJQUVBLE1BQU1FLGdCQUFxQztRQUN6QyxJQUFJO1lBQ0YsSUFBSSxDQUFDLElBQUksQ0FBQzFCLE1BQU0sRUFBRVUsY0FBYztnQkFDOUIsTUFBTSxJQUFJaUIsTUFBTTtZQUNsQjtZQUVBLElBQUksQ0FBQ3RDLFlBQVksQ0FBQ2lCLGNBQWMsQ0FBQztnQkFDL0JHLGVBQWUsSUFBSSxDQUFDVCxNQUFNLENBQUNVLFlBQVk7WUFDekM7WUFFQSxNQUFNLEVBQUVrQixXQUFXLEVBQUUsR0FBRyxNQUFNLElBQUksQ0FBQ3ZDLFlBQVksQ0FBQ3dDLGtCQUFrQjtZQUVsRSxNQUFNUCxhQUF5QjtnQkFDN0JkLGFBQWFvQixZQUFZckIsWUFBWTtnQkFDckNHLGNBQWNrQixZQUFZbkIsYUFBYSxJQUFJLElBQUksQ0FBQ1QsTUFBTSxDQUFDVSxZQUFZO2dCQUNuRVIsV0FBVzBCLFlBQVlqQixXQUFXLEdBQUcsSUFBSVIsS0FBS3lCLFlBQVlqQixXQUFXLElBQUlOO2dCQUN6RU8sT0FBT2dCLFlBQVloQixLQUFLLEVBQUVXLE1BQU07WUFDbEM7WUFFQSxJQUFJLENBQUN4QixTQUFTLENBQUN1QjtZQUNmLElBQUksQ0FBQ2pDLFlBQVksQ0FBQ2lCLGNBQWMsQ0FBQ3NCO1lBRWpDLE9BQU9OO1FBQ1QsRUFBRSxPQUFPRSxPQUFPO1lBQ2QsTUFBTSxJQUFJLENBQUNDLFdBQVcsQ0FBQ0QsT0FBTztRQUNoQztJQUNGO0lBRUEsTUFBTU0sY0FBNEI7UUFDaEMsSUFBSTtZQUNGLE1BQU1DLFNBQVMvQyw4Q0FBTUEsQ0FBQytDLE1BQU0sQ0FBQztnQkFBRWpDLFNBQVM7Z0JBQU1SLE1BQU0sSUFBSSxDQUFDRCxZQUFZO1lBQUM7WUFDdEUsTUFBTSxFQUFFMkMsSUFBSSxFQUFFLEdBQUcsTUFBTUQsT0FBT0UsUUFBUSxDQUFDQyxHQUFHO1lBRTFDLE9BQU87Z0JBQ0xDLElBQUlILEtBQUtHLEVBQUU7Z0JBQ1hDLE1BQU1KLEtBQUtJLElBQUk7Z0JBQ2ZDLE9BQU9MLEtBQUtLLEtBQUs7Z0JBQ2pCQyxRQUFRTixLQUFLTyxPQUFPO1lBQ3RCO1FBQ0YsRUFBRSxPQUFPZixPQUFPO1lBQ2QsTUFBTSxJQUFJLENBQUNDLFdBQVcsQ0FBQ0QsT0FBTztRQUNoQztJQUNGO0lBRUEsTUFBTWdCLFVBQVVDLFdBQW1CLE1BQU0sRUFBRUMsU0FBa0IsRUFHMUQ7UUFDRCxJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNLElBQUksQ0FBQzlDLEtBQUssQ0FBQytDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUMzQ0MsR0FBRyxDQUFDLENBQUMsRUFBRUwsU0FBUyw4QkFBOEIsQ0FBQztnQkFDL0NNLFFBQVE7Z0JBQ1JDLFVBQVU7Z0JBQ1ZOO1lBQ0Y7WUFFQSxNQUFNRSxRQUFxQkQsU0FBU1gsSUFBSSxDQUFDWSxLQUFLLENBQUNLLEdBQUcsQ0FBQyxDQUFDQyxPQUFlO29CQUNqRWYsSUFBSWUsS0FBS2YsRUFBRTtvQkFDWEMsTUFBTWMsS0FBS2QsSUFBSTtvQkFDZmUsTUFBTUMsU0FBU0YsS0FBS0MsSUFBSSxLQUFLO29CQUM3QkUsVUFBVUgsS0FBS0csUUFBUTtvQkFDdkJDLFdBQVcsSUFBSSxDQUFDQyxnQkFBZ0IsQ0FBQ0wsS0FBS2QsSUFBSTtvQkFDMUNvQixNQUFNLENBQUMsQ0FBQyxFQUFFTixLQUFLZCxJQUFJLEVBQUU7b0JBQ3JCcUIsVUFBVSxJQUFJLENBQUNBLFFBQVE7b0JBQ3ZCQyxhQUFhUixLQUFLUyxjQUFjO29CQUNoQ0MsY0FBY1YsS0FBS1csYUFBYTtvQkFDaENDLFdBQVcsSUFBSTNELEtBQUsrQyxLQUFLYSxXQUFXO29CQUNwQ0MsWUFBWSxJQUFJN0QsS0FBSytDLEtBQUtlLFlBQVk7Z0JBQ3hDO1lBRUEsT0FBTztnQkFDTHJCLE9BQU9BLE1BQU1zQixNQUFNLENBQUNoQixDQUFBQSxPQUFRLElBQUksQ0FBQ2lCLFlBQVksQ0FBQ2pCO2dCQUM5Q2tCLGVBQWV6QixTQUFTWCxJQUFJLENBQUNvQyxhQUFhO1lBQzVDO1FBQ0YsRUFBRSxPQUFPNUMsT0FBTztZQUNkLE1BQU0sSUFBSSxDQUFDQyxXQUFXLENBQUNELE9BQU87UUFDaEM7SUFDRjtJQUVBLE1BQU02QyxhQUFhQyxNQUFjLEVBQW1CO1FBQ2xELElBQUk7WUFDRixNQUFNM0IsV0FBVyxNQUFNLElBQUksQ0FBQzlDLEtBQUssQ0FBQytDLEtBQUssQ0FBQ1YsR0FBRyxDQUFDO2dCQUMxQ29DO2dCQUNBQyxLQUFLO1lBQ1AsR0FBRztnQkFDREMsY0FBYztZQUNoQjtZQUVBLE9BQU9DLE9BQU9DLElBQUksQ0FBQy9CLFNBQVNYLElBQUk7UUFDbEMsRUFBRSxPQUFPUixPQUFPO1lBQ2QsTUFBTSxJQUFJLENBQUNDLFdBQVcsQ0FBQ0QsT0FBTztRQUNoQztJQUNGO0lBRUEsTUFBTW1ELFdBQVdDLFFBQWdCLEVBQUVDLE9BQWUsRUFBRXBDLFdBQW1CLE1BQU0sRUFBc0I7UUFDakcsSUFBSTtZQUNGLGdEQUFnRDtZQUNoRCxNQUFNLEVBQUVxQyxRQUFRLEVBQUUsR0FBR0MsbUJBQU9BLENBQUMsc0JBQVE7WUFDckMsTUFBTUMsU0FBUyxJQUFJRjtZQUNuQkUsT0FBT0MsSUFBSSxDQUFDSjtZQUNaRyxPQUFPQyxJQUFJLENBQUMsT0FBTyxpQkFBaUI7WUFFcEMsTUFBTUMsUUFBUTtnQkFDWjdCLFVBQVU7Z0JBQ1Y4QixNQUFNSDtZQUNSO1lBRUEsTUFBTUksZUFBZTtnQkFDbkJoRCxNQUFNd0M7Z0JBQ05TLFNBQVM7b0JBQUM1QztpQkFBUztZQUNyQjtZQUVBLE1BQU1FLFdBQVcsTUFBTSxJQUFJLENBQUM5QyxLQUFLLENBQUMrQyxLQUFLLENBQUMwQyxNQUFNLENBQUM7Z0JBQzdDQyxVQUFVSDtnQkFDVkY7Z0JBQ0FuQyxRQUFRO1lBQ1Y7WUFFQSxNQUFNRyxPQUFPUCxTQUFTWCxJQUFJO1lBQzFCLE9BQU87Z0JBQ0xHLElBQUllLEtBQUtmLEVBQUU7Z0JBQ1hDLE1BQU1jLEtBQUtkLElBQUk7Z0JBQ2ZlLE1BQU1DLFNBQVNGLEtBQUtDLElBQUksS0FBSzBCLFFBQVFXLE1BQU07Z0JBQzNDbkMsVUFBVUgsS0FBS0csUUFBUTtnQkFDdkJDLFdBQVcsSUFBSSxDQUFDQyxnQkFBZ0IsQ0FBQ0wsS0FBS2QsSUFBSTtnQkFDMUNvQixNQUFNLENBQUMsQ0FBQyxFQUFFTixLQUFLZCxJQUFJLEVBQUU7Z0JBQ3JCcUIsVUFBVSxJQUFJLENBQUNBLFFBQVE7Z0JBQ3ZCSyxXQUFXLElBQUkzRCxLQUFLK0MsS0FBS2EsV0FBVztnQkFDcENDLFlBQVksSUFBSTdELEtBQUsrQyxLQUFLZSxZQUFZO1lBQ3hDO1FBQ0YsRUFBRSxPQUFPekMsT0FBTztZQUNkLE1BQU0sSUFBSSxDQUFDQyxXQUFXLENBQUNELE9BQU87UUFDaEM7SUFDRjtJQUVBLE1BQU1pRSxhQUFhckQsSUFBWSxFQUFFc0QsV0FBbUIsTUFBTSxFQUFtQjtRQUMzRSxJQUFJO1lBQ0YsTUFBTU4sZUFBZTtnQkFDbkJoRDtnQkFDQWlCLFVBQVU7Z0JBQ1ZnQyxTQUFTO29CQUFDSztpQkFBUztZQUNyQjtZQUVBLE1BQU0vQyxXQUFXLE1BQU0sSUFBSSxDQUFDOUMsS0FBSyxDQUFDK0MsS0FBSyxDQUFDMEMsTUFBTSxDQUFDO2dCQUM3Q0MsVUFBVUg7Z0JBQ1ZyQyxRQUFRO1lBQ1Y7WUFFQSxPQUFPSixTQUFTWCxJQUFJLENBQUNHLEVBQUU7UUFDekIsRUFBRSxPQUFPWCxPQUFPO1lBQ2QsTUFBTSxJQUFJLENBQUNDLFdBQVcsQ0FBQ0QsT0FBTztRQUNoQztJQUNGO0lBRUEsTUFBTW1FLFdBQVdyQixNQUFjLEVBQWlCO1FBQzlDLElBQUk7WUFDRixNQUFNLElBQUksQ0FBQ3pFLEtBQUssQ0FBQytDLEtBQUssQ0FBQ2dELE1BQU0sQ0FBQztnQkFBRXRCO1lBQU87UUFDekMsRUFBRSxPQUFPOUMsT0FBTztZQUNkLE1BQU0sSUFBSSxDQUFDQyxXQUFXLENBQUNELE9BQU87UUFDaEM7SUFDRjtJQUVBLE1BQU1xRSxlQUFldkIsTUFBYyxFQUFtQjtRQUNwRCxJQUFJO1lBQ0YsTUFBTTNCLFdBQVcsTUFBTSxJQUFJLENBQUM5QyxLQUFLLENBQUMrQyxLQUFLLENBQUNWLEdBQUcsQ0FBQztnQkFDMUNvQztnQkFDQXZCLFFBQVE7WUFDVjtZQUVBLE9BQU9KLFNBQVNYLElBQUksQ0FBQzJCLGNBQWM7UUFDckMsRUFBRSxPQUFPbkMsT0FBTztZQUNkLE1BQU0sSUFBSSxDQUFDQyxXQUFXLENBQUNELE9BQU87UUFDaEM7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTXNFLDhCQUErQztRQUNuRCxJQUFJO1lBQ0YsZ0RBQWdEO1lBQ2hELE1BQU1uRCxXQUFXLE1BQU0sSUFBSSxDQUFDOUMsS0FBSyxDQUFDK0MsS0FBSyxDQUFDQyxJQUFJLENBQUM7Z0JBQzNDQyxHQUFHO2dCQUNIQyxRQUFRO1lBQ1Y7WUFFQSxJQUFJSixTQUFTWCxJQUFJLENBQUNZLEtBQUssSUFBSUQsU0FBU1gsSUFBSSxDQUFDWSxLQUFLLENBQUM0QyxNQUFNLEdBQUcsR0FBRztnQkFDekQsT0FBTzdDLFNBQVNYLElBQUksQ0FBQ1ksS0FBSyxDQUFDLEVBQUUsQ0FBQ1QsRUFBRTtZQUNsQztZQUVBLG1EQUFtRDtZQUNuRCxPQUFPLE1BQU0sSUFBSSxDQUFDc0QsWUFBWSxDQUFDO1FBQ2pDLEVBQUUsT0FBT2pFLE9BQU87WUFDZCxNQUFNLElBQUksQ0FBQ0MsV0FBVyxDQUFDRCxPQUFPO1FBQ2hDO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsiL2hvbWUvY2FyZGluYWx2aXNpb24vRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvem10LWNsb3VkL3NyYy9saWIvY2xvdWQtc3RvcmFnZS9nb29nbGUtZHJpdmUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ29vZ2xlIH0gZnJvbSAnZ29vZ2xlYXBpcyc7XG5pbXBvcnQgeyBDbG91ZEZpbGUsIEF1dGhUb2tlbnMgfSBmcm9tICcuLi90eXBlcyc7XG5pbXBvcnQgeyBDbG91ZFN0b3JhZ2VQcm92aWRlciB9IGZyb20gJy4vYmFzZSc7XG5cbmV4cG9ydCBjbGFzcyBHb29nbGVEcml2ZVByb3ZpZGVyIGV4dGVuZHMgQ2xvdWRTdG9yYWdlUHJvdmlkZXIge1xuICBwcml2YXRlIG9hdXRoMkNsaWVudDogYW55O1xuICBwcml2YXRlIGRyaXZlOiBhbnk7XG5cbiAgY29uc3RydWN0b3IoKSB7XG4gICAgc3VwZXIoJ2dvb2dsZS1kcml2ZScpO1xuICAgIHRoaXMuaW5pdGlhbGl6ZUNsaWVudCgpO1xuICB9XG5cbiAgcHJpdmF0ZSBpbml0aWFsaXplQ2xpZW50KCk6IHZvaWQge1xuICAgIHRoaXMub2F1dGgyQ2xpZW50ID0gbmV3IGdvb2dsZS5hdXRoLk9BdXRoMihcbiAgICAgIHByb2Nlc3MuZW52LkdPT0dMRV9DTElFTlRfSUQsXG4gICAgICBwcm9jZXNzLmVudi5HT09HTEVfQ0xJRU5UX1NFQ1JFVCxcbiAgICAgIHByb2Nlc3MuZW52LkdPT0dMRV9SRURJUkVDVF9VUklcbiAgICApO1xuXG4gICAgdGhpcy5kcml2ZSA9IGdvb2dsZS5kcml2ZSh7IHZlcnNpb246ICd2MycsIGF1dGg6IHRoaXMub2F1dGgyQ2xpZW50IH0pO1xuICB9XG5cbiAgLyoqXG4gICAqIE92ZXJyaWRlIHNldFRva2VucyB0byBhbHNvIHVwZGF0ZSBPQXV0aCBjbGllbnQgY3JlZGVudGlhbHNcbiAgICovXG4gIHNldFRva2Vucyh0b2tlbnM6IEF1dGhUb2tlbnMpOiB2b2lkIHtcbiAgICBzdXBlci5zZXRUb2tlbnModG9rZW5zKTtcblxuICAgIC8vIENvbnZlcnQgb3VyIHRva2VuIGZvcm1hdCB0byBHb29nbGUncyBmb3JtYXQgYW5kIHNldCBjcmVkZW50aWFsc1xuICAgIGNvbnN0IGV4cGlyeURhdGUgPSB0b2tlbnMuZXhwaXJlc0F0XG4gICAgICA/ICh0b2tlbnMuZXhwaXJlc0F0IGluc3RhbmNlb2YgRGF0ZSA/IHRva2Vucy5leHBpcmVzQXQuZ2V0VGltZSgpIDogbmV3IERhdGUodG9rZW5zLmV4cGlyZXNBdCkuZ2V0VGltZSgpKVxuICAgICAgOiB1bmRlZmluZWQ7XG5cbiAgICB0aGlzLm9hdXRoMkNsaWVudC5zZXRDcmVkZW50aWFscyh7XG4gICAgICBhY2Nlc3NfdG9rZW46IHRva2Vucy5hY2Nlc3NUb2tlbixcbiAgICAgIHJlZnJlc2hfdG9rZW46IHRva2Vucy5yZWZyZXNoVG9rZW4sXG4gICAgICBleHBpcnlfZGF0ZTogZXhwaXJ5RGF0ZSxcbiAgICAgIHNjb3BlOiB0b2tlbnMuc2NvcGU/LmpvaW4oJyAnKVxuICAgIH0pO1xuICB9XG5cbiAgZ2V0QXV0aFVybCgpOiBzdHJpbmcge1xuICAgIGNvbnN0IHNjb3BlcyA9IFtcbiAgICAgICdodHRwczovL3d3dy5nb29nbGVhcGlzLmNvbS9hdXRoL2RyaXZlLmZpbGUnLFxuICAgICAgJ2h0dHBzOi8vd3d3Lmdvb2dsZWFwaXMuY29tL2F1dGgvZHJpdmUucmVhZG9ubHknLFxuICAgICAgJ2h0dHBzOi8vd3d3Lmdvb2dsZWFwaXMuY29tL2F1dGgvdXNlcmluZm8ucHJvZmlsZSdcbiAgICBdO1xuXG4gICAgcmV0dXJuIHRoaXMub2F1dGgyQ2xpZW50LmdlbmVyYXRlQXV0aFVybCh7XG4gICAgICBhY2Nlc3NfdHlwZTogJ29mZmxpbmUnLFxuICAgICAgc2NvcGU6IHNjb3BlcyxcbiAgICAgIHByb21wdDogJ2NvbnNlbnQnXG4gICAgfSk7XG4gIH1cblxuICBhc3luYyBhdXRoZW50aWNhdGUoYXV0aENvZGU6IHN0cmluZyk6IFByb21pc2U8QXV0aFRva2Vucz4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IHRva2VucyB9ID0gYXdhaXQgdGhpcy5vYXV0aDJDbGllbnQuZ2V0VG9rZW4oYXV0aENvZGUpO1xuICAgICAgXG4gICAgICBjb25zdCBhdXRoVG9rZW5zOiBBdXRoVG9rZW5zID0ge1xuICAgICAgICBhY2Nlc3NUb2tlbjogdG9rZW5zLmFjY2Vzc190b2tlbixcbiAgICAgICAgcmVmcmVzaFRva2VuOiB0b2tlbnMucmVmcmVzaF90b2tlbixcbiAgICAgICAgZXhwaXJlc0F0OiB0b2tlbnMuZXhwaXJ5X2RhdGUgPyBuZXcgRGF0ZSh0b2tlbnMuZXhwaXJ5X2RhdGUpIDogdW5kZWZpbmVkLFxuICAgICAgICBzY29wZTogdG9rZW5zLnNjb3BlPy5zcGxpdCgnICcpXG4gICAgICB9O1xuXG4gICAgICB0aGlzLnNldFRva2VucyhhdXRoVG9rZW5zKTtcbiAgICAgIHRoaXMub2F1dGgyQ2xpZW50LnNldENyZWRlbnRpYWxzKHRva2Vucyk7XG5cbiAgICAgIHJldHVybiBhdXRoVG9rZW5zO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0aHJvdyB0aGlzLmhhbmRsZUVycm9yKGVycm9yLCAnYXV0aGVudGljYXRpb24nKTtcbiAgICB9XG4gIH1cblxuICBhc3luYyByZWZyZXNoVG9rZW5zKCk6IFByb21pc2U8QXV0aFRva2Vucz4ge1xuICAgIHRyeSB7XG4gICAgICBpZiAoIXRoaXMudG9rZW5zPy5yZWZyZXNoVG9rZW4pIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdObyByZWZyZXNoIHRva2VuIGF2YWlsYWJsZScpO1xuICAgICAgfVxuXG4gICAgICB0aGlzLm9hdXRoMkNsaWVudC5zZXRDcmVkZW50aWFscyh7XG4gICAgICAgIHJlZnJlc2hfdG9rZW46IHRoaXMudG9rZW5zLnJlZnJlc2hUb2tlblxuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IHsgY3JlZGVudGlhbHMgfSA9IGF3YWl0IHRoaXMub2F1dGgyQ2xpZW50LnJlZnJlc2hBY2Nlc3NUb2tlbigpO1xuICAgICAgXG4gICAgICBjb25zdCBhdXRoVG9rZW5zOiBBdXRoVG9rZW5zID0ge1xuICAgICAgICBhY2Nlc3NUb2tlbjogY3JlZGVudGlhbHMuYWNjZXNzX3Rva2VuISxcbiAgICAgICAgcmVmcmVzaFRva2VuOiBjcmVkZW50aWFscy5yZWZyZXNoX3Rva2VuIHx8IHRoaXMudG9rZW5zLnJlZnJlc2hUb2tlbixcbiAgICAgICAgZXhwaXJlc0F0OiBjcmVkZW50aWFscy5leHBpcnlfZGF0ZSA/IG5ldyBEYXRlKGNyZWRlbnRpYWxzLmV4cGlyeV9kYXRlKSA6IHVuZGVmaW5lZCxcbiAgICAgICAgc2NvcGU6IGNyZWRlbnRpYWxzLnNjb3BlPy5zcGxpdCgnICcpXG4gICAgICB9O1xuXG4gICAgICB0aGlzLnNldFRva2VucyhhdXRoVG9rZW5zKTtcbiAgICAgIHRoaXMub2F1dGgyQ2xpZW50LnNldENyZWRlbnRpYWxzKGNyZWRlbnRpYWxzKTtcblxuICAgICAgcmV0dXJuIGF1dGhUb2tlbnM7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHRocm93IHRoaXMuaGFuZGxlRXJyb3IoZXJyb3IsICd0b2tlbiByZWZyZXNoJyk7XG4gICAgfVxuICB9XG5cbiAgYXN5bmMgZ2V0VXNlckluZm8oKTogUHJvbWlzZTxhbnk+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3Qgb2F1dGgyID0gZ29vZ2xlLm9hdXRoMih7IHZlcnNpb246ICd2MicsIGF1dGg6IHRoaXMub2F1dGgyQ2xpZW50IH0pO1xuICAgICAgY29uc3QgeyBkYXRhIH0gPSBhd2FpdCBvYXV0aDIudXNlcmluZm8uZ2V0KCk7XG4gICAgICBcbiAgICAgIHJldHVybiB7XG4gICAgICAgIGlkOiBkYXRhLmlkLFxuICAgICAgICBuYW1lOiBkYXRhLm5hbWUsXG4gICAgICAgIGVtYWlsOiBkYXRhLmVtYWlsLFxuICAgICAgICBhdmF0YXI6IGRhdGEucGljdHVyZVxuICAgICAgfTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdGhyb3cgdGhpcy5oYW5kbGVFcnJvcihlcnJvciwgJ2dldCB1c2VyIGluZm8nKTtcbiAgICB9XG4gIH1cblxuICBhc3luYyBsaXN0RmlsZXMoZm9sZGVySWQ6IHN0cmluZyA9ICdyb290JywgcGFnZVRva2VuPzogc3RyaW5nKTogUHJvbWlzZTx7XG4gICAgZmlsZXM6IENsb3VkRmlsZVtdO1xuICAgIG5leHRQYWdlVG9rZW4/OiBzdHJpbmc7XG4gIH0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmRyaXZlLmZpbGVzLmxpc3Qoe1xuICAgICAgICBxOiBgJyR7Zm9sZGVySWR9JyBpbiBwYXJlbnRzIGFuZCB0cmFzaGVkPWZhbHNlYCxcbiAgICAgICAgZmllbGRzOiAnbmV4dFBhZ2VUb2tlbiwgZmlsZXMoaWQsIG5hbWUsIHNpemUsIG1pbWVUeXBlLCBjcmVhdGVkVGltZSwgbW9kaWZpZWRUaW1lLCB0aHVtYm5haWxMaW5rLCB3ZWJDb250ZW50TGluayknLFxuICAgICAgICBwYWdlU2l6ZTogMTAwLFxuICAgICAgICBwYWdlVG9rZW5cbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCBmaWxlczogQ2xvdWRGaWxlW10gPSByZXNwb25zZS5kYXRhLmZpbGVzLm1hcCgoZmlsZTogYW55KSA9PiAoe1xuICAgICAgICBpZDogZmlsZS5pZCxcbiAgICAgICAgbmFtZTogZmlsZS5uYW1lLFxuICAgICAgICBzaXplOiBwYXJzZUludChmaWxlLnNpemUpIHx8IDAsXG4gICAgICAgIG1pbWVUeXBlOiBmaWxlLm1pbWVUeXBlLFxuICAgICAgICBleHRlbnNpb246IHRoaXMuZ2V0RmlsZUV4dGVuc2lvbihmaWxlLm5hbWUpLFxuICAgICAgICBwYXRoOiBgLyR7ZmlsZS5uYW1lfWAsXG4gICAgICAgIHByb3ZpZGVyOiB0aGlzLnByb3ZpZGVyLFxuICAgICAgICBkb3dubG9hZFVybDogZmlsZS53ZWJDb250ZW50TGluayxcbiAgICAgICAgdGh1bWJuYWlsVXJsOiBmaWxlLnRodW1ibmFpbExpbmssXG4gICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoZmlsZS5jcmVhdGVkVGltZSksXG4gICAgICAgIG1vZGlmaWVkQXQ6IG5ldyBEYXRlKGZpbGUubW9kaWZpZWRUaW1lKVxuICAgICAgfSkpO1xuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBmaWxlczogZmlsZXMuZmlsdGVyKGZpbGUgPT4gdGhpcy52YWxpZGF0ZUZpbGUoZmlsZSkpLFxuICAgICAgICBuZXh0UGFnZVRva2VuOiByZXNwb25zZS5kYXRhLm5leHRQYWdlVG9rZW5cbiAgICAgIH07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHRocm93IHRoaXMuaGFuZGxlRXJyb3IoZXJyb3IsICdsaXN0IGZpbGVzJyk7XG4gICAgfVxuICB9XG5cbiAgYXN5bmMgZG93bmxvYWRGaWxlKGZpbGVJZDogc3RyaW5nKTogUHJvbWlzZTxCdWZmZXI+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmRyaXZlLmZpbGVzLmdldCh7XG4gICAgICAgIGZpbGVJZCxcbiAgICAgICAgYWx0OiAnbWVkaWEnXG4gICAgICB9LCB7XG4gICAgICAgIHJlc3BvbnNlVHlwZTogJ2FycmF5YnVmZmVyJ1xuICAgICAgfSk7XG5cbiAgICAgIHJldHVybiBCdWZmZXIuZnJvbShyZXNwb25zZS5kYXRhKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdGhyb3cgdGhpcy5oYW5kbGVFcnJvcihlcnJvciwgJ2Rvd25sb2FkIGZpbGUnKTtcbiAgICB9XG4gIH1cblxuICBhc3luYyB1cGxvYWRGaWxlKGZpbGVuYW1lOiBzdHJpbmcsIGNvbnRlbnQ6IEJ1ZmZlciwgZm9sZGVySWQ6IHN0cmluZyA9ICdyb290Jyk6IFByb21pc2U8Q2xvdWRGaWxlPiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIENvbnZlcnQgQnVmZmVyIHRvIHN0cmVhbSBmb3IgR29vZ2xlIERyaXZlIEFQSVxuICAgICAgY29uc3QgeyBSZWFkYWJsZSB9ID0gcmVxdWlyZSgnc3RyZWFtJyk7XG4gICAgICBjb25zdCBzdHJlYW0gPSBuZXcgUmVhZGFibGUoKTtcbiAgICAgIHN0cmVhbS5wdXNoKGNvbnRlbnQpO1xuICAgICAgc3RyZWFtLnB1c2gobnVsbCk7IC8vIEVuZCB0aGUgc3RyZWFtXG5cbiAgICAgIGNvbnN0IG1lZGlhID0ge1xuICAgICAgICBtaW1lVHlwZTogJ2FwcGxpY2F0aW9uL29jdGV0LXN0cmVhbScsXG4gICAgICAgIGJvZHk6IHN0cmVhbVxuICAgICAgfTtcblxuICAgICAgY29uc3QgZmlsZU1ldGFkYXRhID0ge1xuICAgICAgICBuYW1lOiBmaWxlbmFtZSxcbiAgICAgICAgcGFyZW50czogW2ZvbGRlcklkXVxuICAgICAgfTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmRyaXZlLmZpbGVzLmNyZWF0ZSh7XG4gICAgICAgIHJlc291cmNlOiBmaWxlTWV0YWRhdGEsXG4gICAgICAgIG1lZGlhLFxuICAgICAgICBmaWVsZHM6ICdpZCwgbmFtZSwgc2l6ZSwgbWltZVR5cGUsIGNyZWF0ZWRUaW1lLCBtb2RpZmllZFRpbWUnXG4gICAgICB9KTtcblxuICAgICAgY29uc3QgZmlsZSA9IHJlc3BvbnNlLmRhdGE7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBpZDogZmlsZS5pZCxcbiAgICAgICAgbmFtZTogZmlsZS5uYW1lLFxuICAgICAgICBzaXplOiBwYXJzZUludChmaWxlLnNpemUpIHx8IGNvbnRlbnQubGVuZ3RoLFxuICAgICAgICBtaW1lVHlwZTogZmlsZS5taW1lVHlwZSxcbiAgICAgICAgZXh0ZW5zaW9uOiB0aGlzLmdldEZpbGVFeHRlbnNpb24oZmlsZS5uYW1lKSxcbiAgICAgICAgcGF0aDogYC8ke2ZpbGUubmFtZX1gLFxuICAgICAgICBwcm92aWRlcjogdGhpcy5wcm92aWRlcixcbiAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZShmaWxlLmNyZWF0ZWRUaW1lKSxcbiAgICAgICAgbW9kaWZpZWRBdDogbmV3IERhdGUoZmlsZS5tb2RpZmllZFRpbWUpXG4gICAgICB9O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0aHJvdyB0aGlzLmhhbmRsZUVycm9yKGVycm9yLCAndXBsb2FkIGZpbGUnKTtcbiAgICB9XG4gIH1cblxuICBhc3luYyBjcmVhdGVGb2xkZXIobmFtZTogc3RyaW5nLCBwYXJlbnRJZDogc3RyaW5nID0gJ3Jvb3QnKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgZmlsZU1ldGFkYXRhID0ge1xuICAgICAgICBuYW1lLFxuICAgICAgICBtaW1lVHlwZTogJ2FwcGxpY2F0aW9uL3ZuZC5nb29nbGUtYXBwcy5mb2xkZXInLFxuICAgICAgICBwYXJlbnRzOiBbcGFyZW50SWRdXG4gICAgICB9O1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuZHJpdmUuZmlsZXMuY3JlYXRlKHtcbiAgICAgICAgcmVzb3VyY2U6IGZpbGVNZXRhZGF0YSxcbiAgICAgICAgZmllbGRzOiAnaWQnXG4gICAgICB9KTtcblxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEuaWQ7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHRocm93IHRoaXMuaGFuZGxlRXJyb3IoZXJyb3IsICdjcmVhdGUgZm9sZGVyJyk7XG4gICAgfVxuICB9XG5cbiAgYXN5bmMgZGVsZXRlRmlsZShmaWxlSWQ6IHN0cmluZyk6IFByb21pc2U8dm9pZD4ge1xuICAgIHRyeSB7XG4gICAgICBhd2FpdCB0aGlzLmRyaXZlLmZpbGVzLmRlbGV0ZSh7IGZpbGVJZCB9KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdGhyb3cgdGhpcy5oYW5kbGVFcnJvcihlcnJvciwgJ2RlbGV0ZSBmaWxlJyk7XG4gICAgfVxuICB9XG5cbiAgYXN5bmMgZ2V0RG93bmxvYWRVcmwoZmlsZUlkOiBzdHJpbmcpOiBQcm9taXNlPHN0cmluZz4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuZHJpdmUuZmlsZXMuZ2V0KHtcbiAgICAgICAgZmlsZUlkLFxuICAgICAgICBmaWVsZHM6ICd3ZWJDb250ZW50TGluaydcbiAgICAgIH0pO1xuXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS53ZWJDb250ZW50TGluaztcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdGhyb3cgdGhpcy5oYW5kbGVFcnJvcihlcnJvciwgJ2dldCBkb3dubG9hZCBVUkwnKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogRmluZCBvciBjcmVhdGUgdGhlIENvbXByZXNzZWQgZm9sZGVyXG4gICAqL1xuICBhc3luYyBnZXRPckNyZWF0ZUNvbXByZXNzZWRGb2xkZXIoKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgICB0cnkge1xuICAgICAgLy8gRmlyc3QsIHRyeSB0byBmaW5kIGV4aXN0aW5nIENvbXByZXNzZWQgZm9sZGVyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuZHJpdmUuZmlsZXMubGlzdCh7XG4gICAgICAgIHE6IFwibmFtZT0nQ29tcHJlc3NlZCcgYW5kIG1pbWVUeXBlPSdhcHBsaWNhdGlvbi92bmQuZ29vZ2xlLWFwcHMuZm9sZGVyJyBhbmQgdHJhc2hlZD1mYWxzZVwiLFxuICAgICAgICBmaWVsZHM6ICdmaWxlcyhpZCwgbmFtZSknXG4gICAgICB9KTtcblxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuZmlsZXMgJiYgcmVzcG9uc2UuZGF0YS5maWxlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgIHJldHVybiByZXNwb25zZS5kYXRhLmZpbGVzWzBdLmlkO1xuICAgICAgfVxuXG4gICAgICAvLyBDcmVhdGUgbmV3IENvbXByZXNzZWQgZm9sZGVyIGlmIGl0IGRvZXNuJ3QgZXhpc3RcbiAgICAgIHJldHVybiBhd2FpdCB0aGlzLmNyZWF0ZUZvbGRlcignQ29tcHJlc3NlZCcpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0aHJvdyB0aGlzLmhhbmRsZUVycm9yKGVycm9yLCAnZ2V0IG9yIGNyZWF0ZSBjb21wcmVzc2VkIGZvbGRlcicpO1xuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbImdvb2dsZSIsIkNsb3VkU3RvcmFnZVByb3ZpZGVyIiwiR29vZ2xlRHJpdmVQcm92aWRlciIsImNvbnN0cnVjdG9yIiwiaW5pdGlhbGl6ZUNsaWVudCIsIm9hdXRoMkNsaWVudCIsImF1dGgiLCJPQXV0aDIiLCJwcm9jZXNzIiwiZW52IiwiR09PR0xFX0NMSUVOVF9JRCIsIkdPT0dMRV9DTElFTlRfU0VDUkVUIiwiR09PR0xFX1JFRElSRUNUX1VSSSIsImRyaXZlIiwidmVyc2lvbiIsInNldFRva2VucyIsInRva2VucyIsImV4cGlyeURhdGUiLCJleHBpcmVzQXQiLCJEYXRlIiwiZ2V0VGltZSIsInVuZGVmaW5lZCIsInNldENyZWRlbnRpYWxzIiwiYWNjZXNzX3Rva2VuIiwiYWNjZXNzVG9rZW4iLCJyZWZyZXNoX3Rva2VuIiwicmVmcmVzaFRva2VuIiwiZXhwaXJ5X2RhdGUiLCJzY29wZSIsImpvaW4iLCJnZXRBdXRoVXJsIiwic2NvcGVzIiwiZ2VuZXJhdGVBdXRoVXJsIiwiYWNjZXNzX3R5cGUiLCJwcm9tcHQiLCJhdXRoZW50aWNhdGUiLCJhdXRoQ29kZSIsImdldFRva2VuIiwiYXV0aFRva2VucyIsInNwbGl0IiwiZXJyb3IiLCJoYW5kbGVFcnJvciIsInJlZnJlc2hUb2tlbnMiLCJFcnJvciIsImNyZWRlbnRpYWxzIiwicmVmcmVzaEFjY2Vzc1Rva2VuIiwiZ2V0VXNlckluZm8iLCJvYXV0aDIiLCJkYXRhIiwidXNlcmluZm8iLCJnZXQiLCJpZCIsIm5hbWUiLCJlbWFpbCIsImF2YXRhciIsInBpY3R1cmUiLCJsaXN0RmlsZXMiLCJmb2xkZXJJZCIsInBhZ2VUb2tlbiIsInJlc3BvbnNlIiwiZmlsZXMiLCJsaXN0IiwicSIsImZpZWxkcyIsInBhZ2VTaXplIiwibWFwIiwiZmlsZSIsInNpemUiLCJwYXJzZUludCIsIm1pbWVUeXBlIiwiZXh0ZW5zaW9uIiwiZ2V0RmlsZUV4dGVuc2lvbiIsInBhdGgiLCJwcm92aWRlciIsImRvd25sb2FkVXJsIiwid2ViQ29udGVudExpbmsiLCJ0aHVtYm5haWxVcmwiLCJ0aHVtYm5haWxMaW5rIiwiY3JlYXRlZEF0IiwiY3JlYXRlZFRpbWUiLCJtb2RpZmllZEF0IiwibW9kaWZpZWRUaW1lIiwiZmlsdGVyIiwidmFsaWRhdGVGaWxlIiwibmV4dFBhZ2VUb2tlbiIsImRvd25sb2FkRmlsZSIsImZpbGVJZCIsImFsdCIsInJlc3BvbnNlVHlwZSIsIkJ1ZmZlciIsImZyb20iLCJ1cGxvYWRGaWxlIiwiZmlsZW5hbWUiLCJjb250ZW50IiwiUmVhZGFibGUiLCJyZXF1aXJlIiwic3RyZWFtIiwicHVzaCIsIm1lZGlhIiwiYm9keSIsImZpbGVNZXRhZGF0YSIsInBhcmVudHMiLCJjcmVhdGUiLCJyZXNvdXJjZSIsImxlbmd0aCIsImNyZWF0ZUZvbGRlciIsInBhcmVudElkIiwiZGVsZXRlRmlsZSIsImRlbGV0ZSIsImdldERvd25sb2FkVXJsIiwiZ2V0T3JDcmVhdGVDb21wcmVzc2VkRm9sZGVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/cloud-storage/google-drive.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/compression-config.ts":
/*!***************************************!*\
  !*** ./src/lib/compression-config.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COMPRESSION_CONFIGS: () => (/* binding */ COMPRESSION_CONFIGS),\n/* harmony export */   estimateCompressionRatio: () => (/* binding */ estimateCompressionRatio),\n/* harmony export */   generateCompressionCommand: () => (/* binding */ generateCompressionCommand),\n/* harmony export */   getCompressionConfig: () => (/* binding */ getCompressionConfig),\n/* harmony export */   getFileExtension: () => (/* binding */ getFileExtension),\n/* harmony export */   getScriptPath: () => (/* binding */ getScriptPath),\n/* harmony export */   getSupportedExtensions: () => (/* binding */ getSupportedExtensions),\n/* harmony export */   isFileSupported: () => (/* binding */ isFileSupported)\n/* harmony export */ });\n// ZMT Compression configuration based on PRD\nconst COMPRESSION_CONFIGS = {\n    // Documents and general files - ZMT compression\n    documents: {\n        extensions: [\n            '.txt',\n            '.pdf',\n            '.xls',\n            '.xlsx',\n            '.doc',\n            '.docx',\n            '.psd',\n            '.csv',\n            '.db',\n            '.dcm',\n            '.ppt',\n            '.pptx'\n        ],\n        script: 'zmt',\n        command: './opt/zmt-scripts/zmt a {outputFile} {inputFile}',\n        description: 'ZMT compression for documents and general files'\n    },\n    // Video files - MP4 compression script\n    video: {\n        extensions: [\n            '.mp4',\n            '.mkv',\n            '.3gp',\n            '.avi',\n            '.mov',\n            '.webm'\n        ],\n        script: 'compress_code_mp4_update.sh',\n        command: './opt/zmt-scripts/compress_code_mp4_update.sh mp4',\n        description: 'Video compression using MP4 optimization'\n    },\n    // Audio files - Audio compression script\n    audio: {\n        extensions: [\n            '.mp3',\n            '.aac',\n            '.opus',\n            '.m4a'\n        ],\n        script: 'compress_code_audio.sh',\n        command: './opt/zmt-scripts/compress_code_audio.sh mp3',\n        description: 'Audio compression optimization'\n    },\n    // Y4M video files - Specialized video compression\n    y4m: {\n        extensions: [\n            '.y4m'\n        ],\n        script: 'compress_code_video.sh',\n        command: './opt/zmt-scripts/compress_code_video.sh',\n        description: 'Y4M video compression'\n    },\n    // TIFF images - Python image compression\n    tiff: {\n        extensions: [\n            '.tif',\n            '.tiff'\n        ],\n        script: 'zmt_image.py',\n        command: 'python3 ./opt/zmt-scripts/zmt_image.py {inputFile}',\n        description: 'TIFF image compression using Python'\n    }\n};\n/**\n * Get compression configuration for a file based on its extension\n */ function getCompressionConfig(filename) {\n    const extension = getFileExtension(filename);\n    for (const config of Object.values(COMPRESSION_CONFIGS)){\n        if (config.extensions.includes(extension)) {\n            return config;\n        }\n    }\n    return null;\n}\n/**\n * Get file extension from filename\n */ function getFileExtension(filename) {\n    const lastDotIndex = filename.lastIndexOf('.');\n    if (lastDotIndex === -1) return '';\n    return filename.substring(lastDotIndex).toLowerCase();\n}\n/**\n * Check if a file type is supported for compression\n */ function isFileSupported(filename) {\n    return getCompressionConfig(filename) !== null;\n}\n/**\n * Get all supported file extensions\n */ function getSupportedExtensions() {\n    const extensions = [];\n    Object.values(COMPRESSION_CONFIGS).forEach((config)=>{\n        extensions.push(...config.extensions);\n    });\n    return [\n        ...new Set(extensions)\n    ].sort();\n}\n/**\n * Generate compression command for a specific file\n */ function generateCompressionCommand(inputFile, outputFile, config) {\n    const path = __webpack_require__(/*! path */ \"path\");\n    const projectRoot = process.cwd();\n    // Convert relative paths to absolute paths\n    let command = config.command;\n    // Replace script paths with absolute paths\n    if (command.startsWith('./opt/zmt-scripts/')) {\n        const scriptName = command.split('./opt/zmt-scripts/')[1].split(' ')[0];\n        const absoluteScriptPath = path.resolve(projectRoot, 'opt', 'zmt-scripts', scriptName);\n        command = command.replace(`./opt/zmt-scripts/${scriptName}`, absoluteScriptPath);\n    }\n    return command.replace('{inputFile}', inputFile).replace('{outputFile}', outputFile);\n}\n/**\n * Get compression script path\n */ function getScriptPath(config) {\n    // In production, these would be absolute paths to the compression scripts\n    return `/opt/zmt-scripts/${config.script}`;\n}\n/**\n * Estimate compression ratio based on file type\n */ function estimateCompressionRatio(filename) {\n    const extension = getFileExtension(filename);\n    // Estimated compression ratios based on file types\n    const ratios = {\n        // Documents - good compression\n        '.txt': 0.3,\n        '.pdf': 0.8,\n        '.doc': 0.6,\n        '.docx': 0.7,\n        '.xls': 0.6,\n        '.xlsx': 0.7,\n        '.csv': 0.4,\n        '.ppt': 0.7,\n        '.pptx': 0.8,\n        // Images - moderate compression\n        '.tif': 0.5,\n        '.tiff': 0.5,\n        '.psd': 0.7,\n        // Video - depends on quality settings\n        '.mp4': 0.6,\n        '.mkv': 0.5,\n        '.avi': 0.4,\n        '.mov': 0.6,\n        '.webm': 0.7,\n        '.3gp': 0.8,\n        '.y4m': 0.3,\n        // Audio - good compression\n        '.mp3': 0.8,\n        '.aac': 0.8,\n        '.opus': 0.8,\n        '.m4a': 0.8,\n        // Database and other\n        '.db': 0.5,\n        '.dcm': 0.6\n    };\n    return ratios[extension] || 0.7; // Default 70% of original size\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2NvbXByZXNzaW9uLWNvbmZpZy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUVBLDZDQUE2QztBQUN0QyxNQUFNQSxzQkFBc0Q7SUFDakUsZ0RBQWdEO0lBQ2hEQyxXQUFXO1FBQ1RDLFlBQVk7WUFBQztZQUFRO1lBQVE7WUFBUTtZQUFTO1lBQVE7WUFBUztZQUFRO1lBQVE7WUFBTztZQUFRO1lBQVE7U0FBUTtRQUM5R0MsUUFBUTtRQUNSQyxTQUFTO1FBQ1RDLGFBQWE7SUFDZjtJQUVBLHVDQUF1QztJQUN2Q0MsT0FBTztRQUNMSixZQUFZO1lBQUM7WUFBUTtZQUFRO1lBQVE7WUFBUTtZQUFRO1NBQVE7UUFDN0RDLFFBQVE7UUFDUkMsU0FBUztRQUNUQyxhQUFhO0lBQ2Y7SUFFQSx5Q0FBeUM7SUFDekNFLE9BQU87UUFDTEwsWUFBWTtZQUFDO1lBQVE7WUFBUTtZQUFTO1NBQU87UUFDN0NDLFFBQVE7UUFDUkMsU0FBUztRQUNUQyxhQUFhO0lBQ2Y7SUFFQSxrREFBa0Q7SUFDbERHLEtBQUs7UUFDSE4sWUFBWTtZQUFDO1NBQU87UUFDcEJDLFFBQVE7UUFDUkMsU0FBUztRQUNUQyxhQUFhO0lBQ2Y7SUFFQSx5Q0FBeUM7SUFDekNJLE1BQU07UUFDSlAsWUFBWTtZQUFDO1lBQVE7U0FBUTtRQUM3QkMsUUFBUTtRQUNSQyxTQUFTO1FBQ1RDLGFBQWE7SUFDZjtBQUNGLEVBQUU7QUFFRjs7Q0FFQyxHQUNNLFNBQVNLLHFCQUFxQkMsUUFBZ0I7SUFDbkQsTUFBTUMsWUFBWUMsaUJBQWlCRjtJQUVuQyxLQUFLLE1BQU1HLFVBQVVDLE9BQU9DLE1BQU0sQ0FBQ2hCLHFCQUFzQjtRQUN2RCxJQUFJYyxPQUFPWixVQUFVLENBQUNlLFFBQVEsQ0FBQ0wsWUFBWTtZQUN6QyxPQUFPRTtRQUNUO0lBQ0Y7SUFFQSxPQUFPO0FBQ1Q7QUFFQTs7Q0FFQyxHQUNNLFNBQVNELGlCQUFpQkYsUUFBZ0I7SUFDL0MsTUFBTU8sZUFBZVAsU0FBU1EsV0FBVyxDQUFDO0lBQzFDLElBQUlELGlCQUFpQixDQUFDLEdBQUcsT0FBTztJQUNoQyxPQUFPUCxTQUFTUyxTQUFTLENBQUNGLGNBQWNHLFdBQVc7QUFDckQ7QUFFQTs7Q0FFQyxHQUNNLFNBQVNDLGdCQUFnQlgsUUFBZ0I7SUFDOUMsT0FBT0QscUJBQXFCQyxjQUFjO0FBQzVDO0FBRUE7O0NBRUMsR0FDTSxTQUFTWTtJQUNkLE1BQU1yQixhQUF1QixFQUFFO0lBQy9CYSxPQUFPQyxNQUFNLENBQUNoQixxQkFBcUJ3QixPQUFPLENBQUNWLENBQUFBO1FBQ3pDWixXQUFXdUIsSUFBSSxJQUFJWCxPQUFPWixVQUFVO0lBQ3RDO0lBQ0EsT0FBTztXQUFJLElBQUl3QixJQUFJeEI7S0FBWSxDQUFDeUIsSUFBSTtBQUN0QztBQUVBOztDQUVDLEdBQ00sU0FBU0MsMkJBQ2RDLFNBQWlCLEVBQ2pCQyxVQUFrQixFQUNsQmhCLE1BQXNCO0lBRXRCLE1BQU1pQixPQUFPQyxtQkFBT0EsQ0FBQyxrQkFBTTtJQUMzQixNQUFNQyxjQUFjQyxRQUFRQyxHQUFHO0lBRS9CLDJDQUEyQztJQUMzQyxJQUFJL0IsVUFBVVUsT0FBT1YsT0FBTztJQUU1QiwyQ0FBMkM7SUFDM0MsSUFBSUEsUUFBUWdDLFVBQVUsQ0FBQyx1QkFBdUI7UUFDNUMsTUFBTUMsYUFBYWpDLFFBQVFrQyxLQUFLLENBQUMscUJBQXFCLENBQUMsRUFBRSxDQUFDQSxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7UUFDdkUsTUFBTUMscUJBQXFCUixLQUFLUyxPQUFPLENBQUNQLGFBQWEsT0FBTyxlQUFlSTtRQUMzRWpDLFVBQVVBLFFBQVFxQyxPQUFPLENBQUMsQ0FBQyxrQkFBa0IsRUFBRUosWUFBWSxFQUFFRTtJQUMvRDtJQUVBLE9BQU9uQyxRQUNKcUMsT0FBTyxDQUFDLGVBQWVaLFdBQ3ZCWSxPQUFPLENBQUMsZ0JBQWdCWDtBQUM3QjtBQUVBOztDQUVDLEdBQ00sU0FBU1ksY0FBYzVCLE1BQXNCO0lBQ2xELDBFQUEwRTtJQUMxRSxPQUFPLENBQUMsaUJBQWlCLEVBQUVBLE9BQU9YLE1BQU0sRUFBRTtBQUM1QztBQUVBOztDQUVDLEdBQ00sU0FBU3dDLHlCQUF5QmhDLFFBQWdCO0lBQ3ZELE1BQU1DLFlBQVlDLGlCQUFpQkY7SUFFbkMsbURBQW1EO0lBQ25ELE1BQU1pQyxTQUFpQztRQUNyQywrQkFBK0I7UUFDL0IsUUFBUTtRQUNSLFFBQVE7UUFDUixRQUFRO1FBQ1IsU0FBUztRQUNULFFBQVE7UUFDUixTQUFTO1FBQ1QsUUFBUTtRQUNSLFFBQVE7UUFDUixTQUFTO1FBRVQsZ0NBQWdDO1FBQ2hDLFFBQVE7UUFDUixTQUFTO1FBQ1QsUUFBUTtRQUVSLHNDQUFzQztRQUN0QyxRQUFRO1FBQ1IsUUFBUTtRQUNSLFFBQVE7UUFDUixRQUFRO1FBQ1IsU0FBUztRQUNULFFBQVE7UUFDUixRQUFRO1FBRVIsMkJBQTJCO1FBQzNCLFFBQVE7UUFDUixRQUFRO1FBQ1IsU0FBUztRQUNULFFBQVE7UUFFUixxQkFBcUI7UUFDckIsT0FBTztRQUNQLFFBQVE7SUFDVjtJQUVBLE9BQU9BLE1BQU0sQ0FBQ2hDLFVBQVUsSUFBSSxLQUFLLCtCQUErQjtBQUNsRSIsInNvdXJjZXMiOlsiL2hvbWUvY2FyZGluYWx2aXNpb24vRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvem10LWNsb3VkL3NyYy9saWIvY29tcHJlc3Npb24tY29uZmlnLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEZpbGVUeXBlQ29uZmlnIH0gZnJvbSAnLi90eXBlcyc7XG5cbi8vIFpNVCBDb21wcmVzc2lvbiBjb25maWd1cmF0aW9uIGJhc2VkIG9uIFBSRFxuZXhwb3J0IGNvbnN0IENPTVBSRVNTSU9OX0NPTkZJR1M6IFJlY29yZDxzdHJpbmcsIEZpbGVUeXBlQ29uZmlnPiA9IHtcbiAgLy8gRG9jdW1lbnRzIGFuZCBnZW5lcmFsIGZpbGVzIC0gWk1UIGNvbXByZXNzaW9uXG4gIGRvY3VtZW50czoge1xuICAgIGV4dGVuc2lvbnM6IFsnLnR4dCcsICcucGRmJywgJy54bHMnLCAnLnhsc3gnLCAnLmRvYycsICcuZG9jeCcsICcucHNkJywgJy5jc3YnLCAnLmRiJywgJy5kY20nLCAnLnBwdCcsICcucHB0eCddLFxuICAgIHNjcmlwdDogJ3ptdCcsXG4gICAgY29tbWFuZDogJy4vb3B0L3ptdC1zY3JpcHRzL3ptdCBhIHtvdXRwdXRGaWxlfSB7aW5wdXRGaWxlfScsXG4gICAgZGVzY3JpcHRpb246ICdaTVQgY29tcHJlc3Npb24gZm9yIGRvY3VtZW50cyBhbmQgZ2VuZXJhbCBmaWxlcydcbiAgfSxcblxuICAvLyBWaWRlbyBmaWxlcyAtIE1QNCBjb21wcmVzc2lvbiBzY3JpcHRcbiAgdmlkZW86IHtcbiAgICBleHRlbnNpb25zOiBbJy5tcDQnLCAnLm1rdicsICcuM2dwJywgJy5hdmknLCAnLm1vdicsICcud2VibSddLFxuICAgIHNjcmlwdDogJ2NvbXByZXNzX2NvZGVfbXA0X3VwZGF0ZS5zaCcsXG4gICAgY29tbWFuZDogJy4vb3B0L3ptdC1zY3JpcHRzL2NvbXByZXNzX2NvZGVfbXA0X3VwZGF0ZS5zaCBtcDQnLFxuICAgIGRlc2NyaXB0aW9uOiAnVmlkZW8gY29tcHJlc3Npb24gdXNpbmcgTVA0IG9wdGltaXphdGlvbidcbiAgfSxcblxuICAvLyBBdWRpbyBmaWxlcyAtIEF1ZGlvIGNvbXByZXNzaW9uIHNjcmlwdFxuICBhdWRpbzoge1xuICAgIGV4dGVuc2lvbnM6IFsnLm1wMycsICcuYWFjJywgJy5vcHVzJywgJy5tNGEnXSxcbiAgICBzY3JpcHQ6ICdjb21wcmVzc19jb2RlX2F1ZGlvLnNoJyxcbiAgICBjb21tYW5kOiAnLi9vcHQvem10LXNjcmlwdHMvY29tcHJlc3NfY29kZV9hdWRpby5zaCBtcDMnLFxuICAgIGRlc2NyaXB0aW9uOiAnQXVkaW8gY29tcHJlc3Npb24gb3B0aW1pemF0aW9uJ1xuICB9LFxuXG4gIC8vIFk0TSB2aWRlbyBmaWxlcyAtIFNwZWNpYWxpemVkIHZpZGVvIGNvbXByZXNzaW9uXG4gIHk0bToge1xuICAgIGV4dGVuc2lvbnM6IFsnLnk0bSddLFxuICAgIHNjcmlwdDogJ2NvbXByZXNzX2NvZGVfdmlkZW8uc2gnLFxuICAgIGNvbW1hbmQ6ICcuL29wdC96bXQtc2NyaXB0cy9jb21wcmVzc19jb2RlX3ZpZGVvLnNoJyxcbiAgICBkZXNjcmlwdGlvbjogJ1k0TSB2aWRlbyBjb21wcmVzc2lvbidcbiAgfSxcblxuICAvLyBUSUZGIGltYWdlcyAtIFB5dGhvbiBpbWFnZSBjb21wcmVzc2lvblxuICB0aWZmOiB7XG4gICAgZXh0ZW5zaW9uczogWycudGlmJywgJy50aWZmJ10sXG4gICAgc2NyaXB0OiAnem10X2ltYWdlLnB5JyxcbiAgICBjb21tYW5kOiAncHl0aG9uMyAuL29wdC96bXQtc2NyaXB0cy96bXRfaW1hZ2UucHkge2lucHV0RmlsZX0nLFxuICAgIGRlc2NyaXB0aW9uOiAnVElGRiBpbWFnZSBjb21wcmVzc2lvbiB1c2luZyBQeXRob24nXG4gIH1cbn07XG5cbi8qKlxuICogR2V0IGNvbXByZXNzaW9uIGNvbmZpZ3VyYXRpb24gZm9yIGEgZmlsZSBiYXNlZCBvbiBpdHMgZXh0ZW5zaW9uXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRDb21wcmVzc2lvbkNvbmZpZyhmaWxlbmFtZTogc3RyaW5nKTogRmlsZVR5cGVDb25maWcgfCBudWxsIHtcbiAgY29uc3QgZXh0ZW5zaW9uID0gZ2V0RmlsZUV4dGVuc2lvbihmaWxlbmFtZSk7XG4gIFxuICBmb3IgKGNvbnN0IGNvbmZpZyBvZiBPYmplY3QudmFsdWVzKENPTVBSRVNTSU9OX0NPTkZJR1MpKSB7XG4gICAgaWYgKGNvbmZpZy5leHRlbnNpb25zLmluY2x1ZGVzKGV4dGVuc2lvbikpIHtcbiAgICAgIHJldHVybiBjb25maWc7XG4gICAgfVxuICB9XG4gIFxuICByZXR1cm4gbnVsbDtcbn1cblxuLyoqXG4gKiBHZXQgZmlsZSBleHRlbnNpb24gZnJvbSBmaWxlbmFtZVxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0RmlsZUV4dGVuc2lvbihmaWxlbmFtZTogc3RyaW5nKTogc3RyaW5nIHtcbiAgY29uc3QgbGFzdERvdEluZGV4ID0gZmlsZW5hbWUubGFzdEluZGV4T2YoJy4nKTtcbiAgaWYgKGxhc3REb3RJbmRleCA9PT0gLTEpIHJldHVybiAnJztcbiAgcmV0dXJuIGZpbGVuYW1lLnN1YnN0cmluZyhsYXN0RG90SW5kZXgpLnRvTG93ZXJDYXNlKCk7XG59XG5cbi8qKlxuICogQ2hlY2sgaWYgYSBmaWxlIHR5cGUgaXMgc3VwcG9ydGVkIGZvciBjb21wcmVzc2lvblxuICovXG5leHBvcnQgZnVuY3Rpb24gaXNGaWxlU3VwcG9ydGVkKGZpbGVuYW1lOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgcmV0dXJuIGdldENvbXByZXNzaW9uQ29uZmlnKGZpbGVuYW1lKSAhPT0gbnVsbDtcbn1cblxuLyoqXG4gKiBHZXQgYWxsIHN1cHBvcnRlZCBmaWxlIGV4dGVuc2lvbnNcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldFN1cHBvcnRlZEV4dGVuc2lvbnMoKTogc3RyaW5nW10ge1xuICBjb25zdCBleHRlbnNpb25zOiBzdHJpbmdbXSA9IFtdO1xuICBPYmplY3QudmFsdWVzKENPTVBSRVNTSU9OX0NPTkZJR1MpLmZvckVhY2goY29uZmlnID0+IHtcbiAgICBleHRlbnNpb25zLnB1c2goLi4uY29uZmlnLmV4dGVuc2lvbnMpO1xuICB9KTtcbiAgcmV0dXJuIFsuLi5uZXcgU2V0KGV4dGVuc2lvbnMpXS5zb3J0KCk7XG59XG5cbi8qKlxuICogR2VuZXJhdGUgY29tcHJlc3Npb24gY29tbWFuZCBmb3IgYSBzcGVjaWZpYyBmaWxlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUNvbXByZXNzaW9uQ29tbWFuZChcbiAgaW5wdXRGaWxlOiBzdHJpbmcsXG4gIG91dHB1dEZpbGU6IHN0cmluZyxcbiAgY29uZmlnOiBGaWxlVHlwZUNvbmZpZ1xuKTogc3RyaW5nIHtcbiAgY29uc3QgcGF0aCA9IHJlcXVpcmUoJ3BhdGgnKTtcbiAgY29uc3QgcHJvamVjdFJvb3QgPSBwcm9jZXNzLmN3ZCgpO1xuXG4gIC8vIENvbnZlcnQgcmVsYXRpdmUgcGF0aHMgdG8gYWJzb2x1dGUgcGF0aHNcbiAgbGV0IGNvbW1hbmQgPSBjb25maWcuY29tbWFuZDtcblxuICAvLyBSZXBsYWNlIHNjcmlwdCBwYXRocyB3aXRoIGFic29sdXRlIHBhdGhzXG4gIGlmIChjb21tYW5kLnN0YXJ0c1dpdGgoJy4vb3B0L3ptdC1zY3JpcHRzLycpKSB7XG4gICAgY29uc3Qgc2NyaXB0TmFtZSA9IGNvbW1hbmQuc3BsaXQoJy4vb3B0L3ptdC1zY3JpcHRzLycpWzFdLnNwbGl0KCcgJylbMF07XG4gICAgY29uc3QgYWJzb2x1dGVTY3JpcHRQYXRoID0gcGF0aC5yZXNvbHZlKHByb2plY3RSb290LCAnb3B0JywgJ3ptdC1zY3JpcHRzJywgc2NyaXB0TmFtZSk7XG4gICAgY29tbWFuZCA9IGNvbW1hbmQucmVwbGFjZShgLi9vcHQvem10LXNjcmlwdHMvJHtzY3JpcHROYW1lfWAsIGFic29sdXRlU2NyaXB0UGF0aCk7XG4gIH1cblxuICByZXR1cm4gY29tbWFuZFxuICAgIC5yZXBsYWNlKCd7aW5wdXRGaWxlfScsIGlucHV0RmlsZSlcbiAgICAucmVwbGFjZSgne291dHB1dEZpbGV9Jywgb3V0cHV0RmlsZSk7XG59XG5cbi8qKlxuICogR2V0IGNvbXByZXNzaW9uIHNjcmlwdCBwYXRoXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRTY3JpcHRQYXRoKGNvbmZpZzogRmlsZVR5cGVDb25maWcpOiBzdHJpbmcge1xuICAvLyBJbiBwcm9kdWN0aW9uLCB0aGVzZSB3b3VsZCBiZSBhYnNvbHV0ZSBwYXRocyB0byB0aGUgY29tcHJlc3Npb24gc2NyaXB0c1xuICByZXR1cm4gYC9vcHQvem10LXNjcmlwdHMvJHtjb25maWcuc2NyaXB0fWA7XG59XG5cbi8qKlxuICogRXN0aW1hdGUgY29tcHJlc3Npb24gcmF0aW8gYmFzZWQgb24gZmlsZSB0eXBlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBlc3RpbWF0ZUNvbXByZXNzaW9uUmF0aW8oZmlsZW5hbWU6IHN0cmluZyk6IG51bWJlciB7XG4gIGNvbnN0IGV4dGVuc2lvbiA9IGdldEZpbGVFeHRlbnNpb24oZmlsZW5hbWUpO1xuICBcbiAgLy8gRXN0aW1hdGVkIGNvbXByZXNzaW9uIHJhdGlvcyBiYXNlZCBvbiBmaWxlIHR5cGVzXG4gIGNvbnN0IHJhdGlvczogUmVjb3JkPHN0cmluZywgbnVtYmVyPiA9IHtcbiAgICAvLyBEb2N1bWVudHMgLSBnb29kIGNvbXByZXNzaW9uXG4gICAgJy50eHQnOiAwLjMsXG4gICAgJy5wZGYnOiAwLjgsXG4gICAgJy5kb2MnOiAwLjYsXG4gICAgJy5kb2N4JzogMC43LFxuICAgICcueGxzJzogMC42LFxuICAgICcueGxzeCc6IDAuNyxcbiAgICAnLmNzdic6IDAuNCxcbiAgICAnLnBwdCc6IDAuNyxcbiAgICAnLnBwdHgnOiAwLjgsXG4gICAgXG4gICAgLy8gSW1hZ2VzIC0gbW9kZXJhdGUgY29tcHJlc3Npb25cbiAgICAnLnRpZic6IDAuNSxcbiAgICAnLnRpZmYnOiAwLjUsXG4gICAgJy5wc2QnOiAwLjcsXG4gICAgXG4gICAgLy8gVmlkZW8gLSBkZXBlbmRzIG9uIHF1YWxpdHkgc2V0dGluZ3NcbiAgICAnLm1wNCc6IDAuNixcbiAgICAnLm1rdic6IDAuNSxcbiAgICAnLmF2aSc6IDAuNCxcbiAgICAnLm1vdic6IDAuNixcbiAgICAnLndlYm0nOiAwLjcsXG4gICAgJy4zZ3AnOiAwLjgsXG4gICAgJy55NG0nOiAwLjMsXG4gICAgXG4gICAgLy8gQXVkaW8gLSBnb29kIGNvbXByZXNzaW9uXG4gICAgJy5tcDMnOiAwLjgsIC8vIEFscmVhZHkgY29tcHJlc3NlZFxuICAgICcuYWFjJzogMC44LCAvLyBBbHJlYWR5IGNvbXByZXNzZWRcbiAgICAnLm9wdXMnOiAwLjgsIC8vIEFscmVhZHkgY29tcHJlc3NlZFxuICAgICcubTRhJzogMC44LCAvLyBBbHJlYWR5IGNvbXByZXNzZWRcbiAgICBcbiAgICAvLyBEYXRhYmFzZSBhbmQgb3RoZXJcbiAgICAnLmRiJzogMC41LFxuICAgICcuZGNtJzogMC42XG4gIH07XG4gIFxuICByZXR1cm4gcmF0aW9zW2V4dGVuc2lvbl0gfHwgMC43OyAvLyBEZWZhdWx0IDcwJSBvZiBvcmlnaW5hbCBzaXplXG59XG5cblxuIl0sIm5hbWVzIjpbIkNPTVBSRVNTSU9OX0NPTkZJR1MiLCJkb2N1bWVudHMiLCJleHRlbnNpb25zIiwic2NyaXB0IiwiY29tbWFuZCIsImRlc2NyaXB0aW9uIiwidmlkZW8iLCJhdWRpbyIsInk0bSIsInRpZmYiLCJnZXRDb21wcmVzc2lvbkNvbmZpZyIsImZpbGVuYW1lIiwiZXh0ZW5zaW9uIiwiZ2V0RmlsZUV4dGVuc2lvbiIsImNvbmZpZyIsIk9iamVjdCIsInZhbHVlcyIsImluY2x1ZGVzIiwibGFzdERvdEluZGV4IiwibGFzdEluZGV4T2YiLCJzdWJzdHJpbmciLCJ0b0xvd2VyQ2FzZSIsImlzRmlsZVN1cHBvcnRlZCIsImdldFN1cHBvcnRlZEV4dGVuc2lvbnMiLCJmb3JFYWNoIiwicHVzaCIsIlNldCIsInNvcnQiLCJnZW5lcmF0ZUNvbXByZXNzaW9uQ29tbWFuZCIsImlucHV0RmlsZSIsIm91dHB1dEZpbGUiLCJwYXRoIiwicmVxdWlyZSIsInByb2plY3RSb290IiwicHJvY2VzcyIsImN3ZCIsInN0YXJ0c1dpdGgiLCJzY3JpcHROYW1lIiwic3BsaXQiLCJhYnNvbHV0ZVNjcmlwdFBhdGgiLCJyZXNvbHZlIiwicmVwbGFjZSIsImdldFNjcmlwdFBhdGgiLCJlc3RpbWF0ZUNvbXByZXNzaW9uUmF0aW8iLCJyYXRpb3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/compression-config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/compression/engine.ts":
/*!***************************************!*\
  !*** ./src/lib/compression/engine.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompressionEngine: () => (/* binding */ CompressionEngine)\n/* harmony export */ });\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n/* harmony import */ var _compression_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../compression-config */ \"(rsc)/./src/lib/compression-config.ts\");\n\n\n\n\n\nclass CompressionEngine {\n    constructor(storageManager, tempDir = '/tmp/zmt-cloud'){\n        this.jobs = new Map();\n        this.activeProcesses = new Map();\n        this.storageManager = storageManager;\n        this.tempDir = tempDir;\n        this.ensureTempDir();\n    }\n    /**\n   * Create a new compression job\n   */ async createJob(files) {\n        const jobId = (0,uuid__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n        const now = new Date();\n        // Validate files\n        const validFiles = files.filter((file)=>{\n            const config = (0,_compression_config__WEBPACK_IMPORTED_MODULE_3__.getCompressionConfig)(file.name);\n            return config !== null;\n        });\n        if (validFiles.length === 0) {\n            throw new Error('No supported files found for compression');\n        }\n        const totalSize = validFiles.reduce((sum, file)=>sum + file.size, 0);\n        const job = {\n            id: jobId,\n            files: validFiles,\n            status: 'pending',\n            progress: 0,\n            startTime: now,\n            originalSize: totalSize,\n            logs: [],\n            outputFiles: []\n        };\n        this.jobs.set(jobId, job);\n        this.addLog(jobId, 'info', `Compression job created with ${validFiles.length} files`);\n        return job;\n    }\n    /**\n   * Start compression job\n   */ async startJob(jobId) {\n        const job = this.jobs.get(jobId);\n        if (!job) {\n            throw new Error(`Job ${jobId} not found`);\n        }\n        if (job.status !== 'pending') {\n            throw new Error(`Job ${jobId} is not in pending status`);\n        }\n        job.status = 'processing';\n        job.startTime = new Date();\n        this.addLog(jobId, 'info', 'Starting compression job');\n        try {\n            await this.processJob(job);\n        } catch (error) {\n            job.status = 'failed';\n            job.error = error instanceof Error ? error.message : 'Unknown error';\n            job.endTime = new Date();\n            this.addLog(jobId, 'error', `Job failed: ${job.error}`);\n            throw error;\n        }\n    }\n    /**\n   * Process compression job\n   */ async processJob(job) {\n        const jobDir = path__WEBPACK_IMPORTED_MODULE_2___default().join(this.tempDir, job.id);\n        await fs__WEBPACK_IMPORTED_MODULE_1__.promises.mkdir(jobDir, {\n            recursive: true\n        });\n        try {\n            let processedFiles = 0;\n            const totalFiles = job.files.length;\n            for (const file of job.files){\n                this.addLog(job.id, 'info', `Processing file: ${file.name}`);\n                try {\n                    const outputFile = await this.compressFile(file, jobDir, job.id);\n                    job.outputFiles.push(outputFile);\n                    processedFiles++;\n                    job.progress = Math.round(processedFiles / totalFiles * 100);\n                    this.addLog(job.id, 'info', `Completed: ${file.name}`);\n                } catch (error) {\n                    this.addLog(job.id, 'error', `Failed to compress ${file.name}: ${error}`);\n                // Continue with other files even if one fails\n                }\n            }\n            // Calculate final statistics\n            job.compressedSize = job.outputFiles.reduce((sum, file)=>sum + file.size, 0);\n            job.compressionRatio = job.originalSize > 0 ? job.compressedSize / job.originalSize : 1;\n            job.status = 'completed';\n            job.endTime = new Date();\n            job.duration = job.endTime.getTime() - job.startTime.getTime();\n            this.addLog(job.id, 'info', `Job completed. Original: ${this.formatBytes(job.originalSize)}, ` + `Compressed: ${this.formatBytes(job.compressedSize)}, ` + `Ratio: ${(job.compressionRatio * 100).toFixed(1)}%`);\n        } finally{\n            // Clean up temporary files\n            await this.cleanupJobDir(jobDir);\n        }\n    }\n    /**\n   * Compress a single file\n   */ async compressFile(file, jobDir, jobId) {\n        console.log('=== Starting File Compression ===');\n        console.log('File:', file);\n        console.log('Job directory:', jobDir);\n        const config = (0,_compression_config__WEBPACK_IMPORTED_MODULE_3__.getCompressionConfig)(file.name);\n        if (!config) {\n            throw new Error(`No compression config found for ${file.name}`);\n        }\n        console.log('Compression config:', config);\n        // Download file from cloud storage\n        const provider = this.storageManager.getConnectedProvider(file.provider);\n        if (!provider) {\n            throw new Error(`Provider ${file.provider} not connected`);\n        }\n        console.log('Provider found:', file.provider);\n        const inputPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(jobDir, file.name);\n        const outputPath = this.generateOutputPath(inputPath, config);\n        console.log('Input path:', inputPath);\n        console.log('Output path:', outputPath);\n        // Download file\n        console.log('Downloading file from cloud storage...');\n        const fileContent = await provider.downloadFile(file.id);\n        console.log('Downloaded file size:', fileContent.length, 'bytes');\n        await fs__WEBPACK_IMPORTED_MODULE_1__.promises.writeFile(inputPath, fileContent);\n        console.log('File written to local storage');\n        // Run compression\n        console.log('Starting compression process...');\n        this.addLog(jobId, 'info', `🚀 Starting compression for: ${file.name}`);\n        await this.runCompressionScript(config, inputPath, outputPath, jobId);\n        console.log('Compression completed');\n        // Read compressed file\n        console.log('Reading compressed file...');\n        const compressedContent = await fs__WEBPACK_IMPORTED_MODULE_1__.promises.readFile(outputPath);\n        const compressedFilename = path__WEBPACK_IMPORTED_MODULE_2___default().basename(outputPath);\n        console.log('Compressed file size:', compressedContent.length, 'bytes');\n        console.log('Compressed filename:', compressedFilename);\n        // Upload compressed file back to cloud storage\n        console.log('Uploading compressed file to cloud storage...');\n        const compressedFolderId = await this.getCompressedFolderId(provider);\n        console.log('Compressed folder ID:', compressedFolderId);\n        const uploadedFile = await provider.uploadFile(compressedFilename, compressedContent, compressedFolderId);\n        console.log('Upload completed:', uploadedFile);\n        return uploadedFile;\n    }\n    /**\n   * Run compression script\n   */ async runCompressionScript(config, inputPath, outputPath, jobId) {\n        return new Promise((resolve, reject)=>{\n            const scriptPath = (0,_compression_config__WEBPACK_IMPORTED_MODULE_3__.getScriptPath)(config);\n            const command = (0,_compression_config__WEBPACK_IMPORTED_MODULE_3__.generateCompressionCommand)(inputPath, outputPath, config);\n            // Log detailed compression information\n            console.log('=== Compression Script Execution ===');\n            console.log('Config:', config);\n            console.log('Script path:', scriptPath);\n            console.log('Input path:', inputPath);\n            console.log('Output path:', outputPath);\n            console.log('Generated command:', command);\n            console.log('Working directory:', path__WEBPACK_IMPORTED_MODULE_2___default().dirname(inputPath));\n            // Add this to the compression logs that show in the UI\n            if (jobId) {\n                this.addLog(jobId, 'info', `🔧 Executing command: ${command}`);\n                this.addLog(jobId, 'info', `📁 Working directory: ${path__WEBPACK_IMPORTED_MODULE_2___default().dirname(inputPath)}`);\n            }\n            // Parse command into executable and arguments\n            // Need to handle file paths with spaces properly\n            const parts = command.split(' ');\n            const executable = parts[0];\n            // For ZMT command: zmt a outputfile inputfile\n            // We need to properly handle the file paths which may contain spaces\n            let args = [];\n            if (parts[1] === 'a') {\n                // ZMT archive command: executable a outputfile inputfile\n                args = [\n                    'a',\n                    outputPath,\n                    inputPath // Use the actual input path directly\n                ];\n            } else {\n                // For other commands, use the original parsing\n                args = parts.slice(1);\n            }\n            console.log('Executable:', executable);\n            console.log('Arguments:', args);\n            // Check if executable exists\n            const fs = __webpack_require__(/*! fs */ \"fs\");\n            const executableExists = fs.existsSync(executable);\n            console.log('Executable exists:', executableExists);\n            if (!executableExists) {\n                // Try absolute path from project root\n                const projectRoot = process.cwd();\n                const absoluteExecutable = path__WEBPACK_IMPORTED_MODULE_2___default().resolve(projectRoot, executable);\n                console.log('Trying absolute path:', absoluteExecutable);\n                console.log('Absolute executable exists:', fs.existsSync(absoluteExecutable));\n            }\n            const process = (0,child_process__WEBPACK_IMPORTED_MODULE_0__.spawn)(executable, args, {\n                cwd: path__WEBPACK_IMPORTED_MODULE_2___default().dirname(inputPath),\n                stdio: [\n                    'pipe',\n                    'pipe',\n                    'pipe'\n                ]\n            });\n            let stdout = '';\n            let stderr = '';\n            process.stdout?.on('data', (data)=>{\n                stdout += data.toString();\n            });\n            process.stderr?.on('data', (data)=>{\n                stderr += data.toString();\n            });\n            process.on('close', (code)=>{\n                if (code === 0) {\n                    resolve();\n                } else {\n                    reject(new Error(`Compression failed with code ${code}: ${stderr}`));\n                }\n            });\n            process.on('error', (error)=>{\n                reject(new Error(`Failed to start compression process: ${error.message}`));\n            });\n            // Set timeout for compression (30 minutes)\n            setTimeout(()=>{\n                process.kill('SIGTERM');\n                reject(new Error('Compression timeout'));\n            }, 30 * 60 * 1000);\n        });\n    }\n    /**\n   * Generate output file path\n   */ generateOutputPath(inputPath, config) {\n        const dir = path__WEBPACK_IMPORTED_MODULE_2___default().dirname(inputPath);\n        const basename = path__WEBPACK_IMPORTED_MODULE_2___default().basename(inputPath, path__WEBPACK_IMPORTED_MODULE_2___default().extname(inputPath));\n        // Different output extensions based on compression type\n        let outputExt = '.zmt'; // Default ZMT extension\n        if (config.script.includes('mp4')) {\n            outputExt = '.mp4';\n        } else if (config.script.includes('audio')) {\n            outputExt = '.mp3';\n        } else if (config.script.includes('image')) {\n            outputExt = '.tif';\n        }\n        return path__WEBPACK_IMPORTED_MODULE_2___default().join(dir, `${basename}_compressed${outputExt}`);\n    }\n    /**\n   * Get or create compressed folder ID\n   */ async getCompressedFolderId(provider) {\n        if (typeof provider.getOrCreateCompressedFolder === 'function') {\n            return await provider.getOrCreateCompressedFolder();\n        }\n        // Fallback: create folder in root\n        return await provider.createFolder('Compressed');\n    }\n    /**\n   * Add log entry to job\n   */ addLog(jobId, level, message) {\n        const job = this.jobs.get(jobId);\n        if (!job) return;\n        const log = {\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),\n            timestamp: new Date(),\n            level,\n            message\n        };\n        job.logs.push(log);\n    }\n    /**\n   * Get job by ID\n   */ getJob(jobId) {\n        return this.jobs.get(jobId) || null;\n    }\n    /**\n   * Get all jobs\n   */ getAllJobs() {\n        return Array.from(this.jobs.values());\n    }\n    /**\n   * Cancel job\n   */ async cancelJob(jobId) {\n        const job = this.jobs.get(jobId);\n        if (!job) {\n            throw new Error(`Job ${jobId} not found`);\n        }\n        const process = this.activeProcesses.get(jobId);\n        if (process) {\n            process.kill('SIGTERM');\n            this.activeProcesses.delete(jobId);\n        }\n        job.status = 'failed';\n        job.error = 'Job cancelled by user';\n        job.endTime = new Date();\n        this.addLog(jobId, 'info', 'Job cancelled');\n    }\n    /**\n   * Clean up job directory\n   */ async cleanupJobDir(jobDir) {\n        try {\n            await fs__WEBPACK_IMPORTED_MODULE_1__.promises.rm(jobDir, {\n                recursive: true,\n                force: true\n            });\n        } catch (error) {\n            console.error('Failed to cleanup job directory:', error);\n        }\n    }\n    /**\n   * Ensure temp directory exists\n   */ async ensureTempDir() {\n        try {\n            await fs__WEBPACK_IMPORTED_MODULE_1__.promises.mkdir(this.tempDir, {\n                recursive: true\n            });\n        } catch (error) {\n            console.error('Failed to create temp directory:', error);\n        }\n    }\n    /**\n   * Format bytes to human readable string\n   */ formatBytes(bytes) {\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB',\n            'TB'\n        ];\n        if (bytes === 0) return '0 Bytes';\n        const i = Math.floor(Math.log(bytes) / Math.log(1024));\n        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/compression/engine.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dropbox":
/*!**************************!*\
  !*** external "dropbox" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("dropbox");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "googleapis":
/*!*****************************!*\
  !*** external "googleapis" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("googleapis");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/uuid"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcompression%2Fjobs%2Froute&page=%2Fapi%2Fcompression%2Fjobs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcompression%2Fjobs%2Froute.ts&appDir=%2Fhome%2Fcardinalvision%2FDocuments%2Faugment-projects%2Fzmt-cloud%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fcardinalvision%2FDocuments%2Faugment-projects%2Fzmt-cloud&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();