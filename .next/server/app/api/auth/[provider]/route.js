/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[provider]/route";
exports.ids = ["app/api/auth/[provider]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5Bprovider%5D%2Froute&page=%2Fapi%2Fauth%2F%5Bprovider%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5Bprovider%5D%2Froute.ts&appDir=%2Fhome%2Fcardinalvision%2FDocuments%2Faugment-projects%2Fzmt-cloud%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fcardinalvision%2FDocuments%2Faugment-projects%2Fzmt-cloud&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5Bprovider%5D%2Froute&page=%2Fapi%2Fauth%2F%5Bprovider%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5Bprovider%5D%2Froute.ts&appDir=%2Fhome%2Fcardinalvision%2FDocuments%2Faugment-projects%2Fzmt-cloud%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fcardinalvision%2FDocuments%2Faugment-projects%2Fzmt-cloud&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_cardinalvision_Documents_augment_projects_zmt_cloud_src_app_api_auth_provider_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/[provider]/route.ts */ \"(rsc)/./src/app/api/auth/[provider]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[provider]/route\",\n        pathname: \"/api/auth/[provider]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[provider]/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/api/auth/[provider]/route.ts\",\n    nextConfigOutput,\n    userland: _home_cardinalvision_Documents_augment_projects_zmt_cloud_src_app_api_auth_provider_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5Bprovider%5D%2Froute&page=%2Fapi%2Fauth%2F%5Bprovider%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5Bprovider%5D%2Froute.ts&appDir=%2Fhome%2Fcardinalvision%2FDocuments%2Faugment-projects%2Fzmt-cloud%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fcardinalvision%2FDocuments%2Faugment-projects%2Fzmt-cloud&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/[provider]/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/auth/[provider]/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_cloud_storage_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/cloud-storage/factory */ \"(rsc)/./src/lib/cloud-storage/factory.ts\");\n\n\nasync function GET(request, { params }) {\n    try {\n        const { provider } = await params;\n        if (!_lib_cloud_storage_factory__WEBPACK_IMPORTED_MODULE_1__.CloudStorageFactory.isProviderSupported(provider)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unsupported provider'\n            }, {\n                status: 400\n            });\n        }\n        const storageProvider = _lib_cloud_storage_factory__WEBPACK_IMPORTED_MODULE_1__.CloudStorageFactory.getProvider(provider);\n        const authUrl = storageProvider.getAuthUrl();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            authUrl\n        });\n    } catch (error) {\n        console.error('Auth URL generation error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to generate auth URL'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request, { params }) {\n    try {\n        const { provider } = await params;\n        const { code } = await request.json();\n        console.log(`Authenticating ${provider} with code:`, code?.substring(0, 20) + '...');\n        console.log('Environment variables check:');\n        console.log('GOOGLE_CLIENT_ID:', process.env.GOOGLE_CLIENT_ID?.substring(0, 20) + '...');\n        console.log('GOOGLE_CLIENT_SECRET:', process.env.GOOGLE_CLIENT_SECRET ? 'Set' : 'Not set');\n        console.log('GOOGLE_REDIRECT_URI:', process.env.GOOGLE_REDIRECT_URI);\n        if (!_lib_cloud_storage_factory__WEBPACK_IMPORTED_MODULE_1__.CloudStorageFactory.isProviderSupported(provider)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unsupported provider'\n            }, {\n                status: 400\n            });\n        }\n        if (!code) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Authorization code is required'\n            }, {\n                status: 400\n            });\n        }\n        // Mock authentication for testing when credentials are not configured\n        if (process.env.ENABLE_MOCK_AUTH === 'true') {\n            console.log('Using mock authentication for testing');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                tokens: {\n                    accessToken: 'mock_access_token',\n                    refreshToken: 'mock_refresh_token',\n                    expiresAt: new Date(Date.now() + 3600000),\n                    scope: [\n                        'drive.file',\n                        'drive.readonly',\n                        'userinfo.profile'\n                    ]\n                },\n                userInfo: {\n                    id: 'mock_user_123',\n                    name: 'Test User',\n                    email: '<EMAIL>',\n                    picture: 'https://via.placeholder.com/150'\n                },\n                provider\n            });\n        }\n        const storageProvider = _lib_cloud_storage_factory__WEBPACK_IMPORTED_MODULE_1__.CloudStorageFactory.getProvider(provider);\n        const tokens = await storageProvider.authenticate(code);\n        const userInfo = await storageProvider.getUserInfo();\n        // In a real application, you would store these tokens securely\n        // For now, we'll return them to be stored client-side\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            tokens,\n            userInfo,\n            provider\n        });\n    } catch (error) {\n        console.error(`${params.provider} authentication error:`, error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[provider]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/cloud-storage/base.ts":
/*!***************************************!*\
  !*** ./src/lib/cloud-storage/base.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CloudStorageProvider: () => (/* binding */ CloudStorageProvider)\n/* harmony export */ });\n/**\n * Base abstract class for cloud storage providers\n */ class CloudStorageProvider {\n    constructor(provider){\n        this.tokens = null;\n        this.provider = provider;\n    }\n    /**\n   * Set authentication tokens\n   */ setTokens(tokens) {\n        this.tokens = tokens;\n    }\n    /**\n   * Get authentication tokens\n   */ getTokens() {\n        return this.tokens;\n    }\n    /**\n   * Check if provider is authenticated\n   */ isAuthenticated() {\n        return this.tokens !== null && this.tokens.accessToken !== '';\n    }\n    /**\n   * Validate file before processing\n   */ validateFile(file) {\n        if (!file.id || !file.name) {\n            return false;\n        }\n        if (file.size <= 0) {\n            return false;\n        }\n        return true;\n    }\n    /**\n   * Convert file size to human readable format\n   */ formatFileSize(bytes) {\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB',\n            'TB'\n        ];\n        if (bytes === 0) return '0 Bytes';\n        const i = Math.floor(Math.log(bytes) / Math.log(1024));\n        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n    }\n    /**\n   * Extract file extension from filename\n   */ getFileExtension(filename) {\n        const lastDotIndex = filename.lastIndexOf('.');\n        if (lastDotIndex === -1) return '';\n        return filename.substring(lastDotIndex).toLowerCase();\n    }\n    /**\n   * Generate unique filename for compressed file\n   */ generateCompressedFilename(originalFilename) {\n        const extension = this.getFileExtension(originalFilename);\n        const nameWithoutExt = originalFilename.substring(0, originalFilename.lastIndexOf('.'));\n        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\n        return `${nameWithoutExt}_compressed_${timestamp}${extension}`;\n    }\n    /**\n   * Handle API errors consistently\n   */ handleError(error, operation) {\n        console.error(`${this.provider} ${operation} error:`, error);\n        if (error.response) {\n            const status = error.response.status;\n            const message = error.response.data?.error?.message || error.message;\n            switch(status){\n                case 401:\n                    return new Error(`Authentication failed: ${message}`);\n                case 403:\n                    return new Error(`Access denied: ${message}`);\n                case 404:\n                    return new Error(`Resource not found: ${message}`);\n                case 429:\n                    return new Error(`Rate limit exceeded: ${message}`);\n                case 500:\n                case 502:\n                case 503:\n                    return new Error(`Server error: ${message}`);\n                default:\n                    return new Error(`API error (${status}): ${message}`);\n            }\n        }\n        if (error.code) {\n            switch(error.code){\n                case 'ENOTFOUND':\n                    return new Error('Network error: Unable to connect to service');\n                case 'ETIMEDOUT':\n                    return new Error('Network error: Request timeout');\n                default:\n                    return new Error(`Network error: ${error.message}`);\n            }\n        }\n        return new Error(`${operation} failed: ${error.message || 'Unknown error'}`);\n    }\n    /**\n   * Retry mechanism for API calls\n   */ async retry(operation, maxRetries = 3, delay = 1000) {\n        let lastError;\n        for(let attempt = 1; attempt <= maxRetries; attempt++){\n            try {\n                return await operation();\n            } catch (error) {\n                lastError = error;\n                if (attempt === maxRetries) {\n                    throw lastError;\n                }\n                // Exponential backoff\n                const waitTime = delay * Math.pow(2, attempt - 1);\n                await new Promise((resolve)=>setTimeout(resolve, waitTime));\n            }\n        }\n        throw lastError;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2Nsb3VkLXN0b3JhZ2UvYmFzZS50cyIsIm1hcHBpbmdzIjoiOzs7O0FBRUE7O0NBRUMsR0FDTSxNQUFlQTtJQUlwQkMsWUFBWUMsUUFBNkIsQ0FBRTthQUZqQ0MsU0FBNEI7UUFHcEMsSUFBSSxDQUFDRCxRQUFRLEdBQUdBO0lBQ2xCO0lBRUE7O0dBRUMsR0FDREUsVUFBVUQsTUFBa0IsRUFBUTtRQUNsQyxJQUFJLENBQUNBLE1BQU0sR0FBR0E7SUFDaEI7SUFFQTs7R0FFQyxHQUNERSxZQUErQjtRQUM3QixPQUFPLElBQUksQ0FBQ0YsTUFBTTtJQUNwQjtJQUVBOztHQUVDLEdBQ0RHLGtCQUEyQjtRQUN6QixPQUFPLElBQUksQ0FBQ0gsTUFBTSxLQUFLLFFBQVEsSUFBSSxDQUFDQSxNQUFNLENBQUNJLFdBQVcsS0FBSztJQUM3RDtJQTJCQTs7R0FFQyxHQUNELGFBQXVCRSxJQUFlLEVBQVc7UUFDL0MsSUFBSSxDQUFDQSxLQUFLQyxFQUFFLElBQUksQ0FBQ0QsS0FBS0UsSUFBSSxFQUFFO1lBQzFCLE9BQU87UUFDVDtRQUVBLElBQUlGLEtBQUtHLElBQUksSUFBSSxHQUFHO1lBQ2xCLE9BQU87UUFDVDtRQUVBLE9BQU87SUFDVDtJQUVBOztHQUVDLEdBQ0QsZUFBeUJFLEtBQWEsRUFBVTtRQUM5QyxNQUFNQyxRQUFRO1lBQUM7WUFBUztZQUFNO1lBQU07WUFBTTtTQUFLO1FBQy9DLElBQUlELFVBQVUsR0FBRyxPQUFPO1FBRXhCLE1BQU1FLElBQUlDLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsR0FBRyxDQUFDTCxTQUFTRyxLQUFLRSxHQUFHLENBQUM7UUFDaEQsT0FBT0YsS0FBS0csS0FBSyxDQUFDTixRQUFRRyxLQUFLSSxHQUFHLENBQUMsTUFBTUwsS0FBSyxPQUFPLE1BQU0sTUFBTUQsS0FBSyxDQUFDQyxFQUFFO0lBQzNFO0lBRUE7O0dBRUMsR0FDRCxpQkFBMkJPLFFBQWdCLEVBQVU7UUFDbkQsTUFBTUMsZUFBZUQsU0FBU0UsV0FBVyxDQUFDO1FBQzFDLElBQUlELGlCQUFpQixDQUFDLEdBQUcsT0FBTztRQUNoQyxPQUFPRCxTQUFTRyxTQUFTLENBQUNGLGNBQWNHLFdBQVc7SUFDckQ7SUFFQTs7R0FFQyxHQUNELDJCQUFxQ0UsZ0JBQXdCLEVBQVU7UUFDckUsTUFBTUMsWUFBWSxJQUFJLENBQUNSLGdCQUFnQixDQUFDTztRQUN4QyxNQUFNRSxpQkFBaUJGLGlCQUFpQkgsU0FBUyxDQUFDLEdBQUdHLGlCQUFpQkosV0FBVyxDQUFDO1FBQ2xGLE1BQU1PLFlBQVksSUFBSUMsT0FBT0MsV0FBVyxHQUFHQyxPQUFPLENBQUMsU0FBUztRQUU1RCxPQUFPLEdBQUdKLGVBQWUsWUFBWSxFQUFFQyxZQUFZRixXQUFXO0lBQ2hFO0lBRUE7O0dBRUMsR0FDRCxZQUFzQk8sS0FBVSxFQUFFQyxTQUFpQixFQUFTO1FBQzFEQyxRQUFRRixLQUFLLENBQUMsR0FBRyxJQUFJLENBQUNuQyxRQUFRLENBQUMsQ0FBQyxFQUFFb0MsVUFBVSxPQUFPLENBQUMsRUFBRUQ7UUFFdEQsSUFBSUEsTUFBTUcsUUFBUSxFQUFFO1lBQ2xCLE1BQU1DLFNBQVNKLE1BQU1HLFFBQVEsQ0FBQ0MsTUFBTTtZQUNwQyxNQUFNQyxVQUFVTCxNQUFNRyxRQUFRLENBQUNHLElBQUksRUFBRU4sT0FBT0ssV0FBV0wsTUFBTUssT0FBTztZQUVwRSxPQUFRRDtnQkFDTixLQUFLO29CQUNILE9BQU8sSUFBSUcsTUFBTSxDQUFDLHVCQUF1QixFQUFFRixTQUFTO2dCQUN0RCxLQUFLO29CQUNILE9BQU8sSUFBSUUsTUFBTSxDQUFDLGVBQWUsRUFBRUYsU0FBUztnQkFDOUMsS0FBSztvQkFDSCxPQUFPLElBQUlFLE1BQU0sQ0FBQyxvQkFBb0IsRUFBRUYsU0FBUztnQkFDbkQsS0FBSztvQkFDSCxPQUFPLElBQUlFLE1BQU0sQ0FBQyxxQkFBcUIsRUFBRUYsU0FBUztnQkFDcEQsS0FBSztnQkFDTCxLQUFLO2dCQUNMLEtBQUs7b0JBQ0gsT0FBTyxJQUFJRSxNQUFNLENBQUMsY0FBYyxFQUFFRixTQUFTO2dCQUM3QztvQkFDRSxPQUFPLElBQUlFLE1BQU0sQ0FBQyxXQUFXLEVBQUVILE9BQU8sR0FBRyxFQUFFQyxTQUFTO1lBQ3hEO1FBQ0Y7UUFFQSxJQUFJTCxNQUFNUSxJQUFJLEVBQUU7WUFDZCxPQUFRUixNQUFNUSxJQUFJO2dCQUNoQixLQUFLO29CQUNILE9BQU8sSUFBSUQsTUFBTTtnQkFDbkIsS0FBSztvQkFDSCxPQUFPLElBQUlBLE1BQU07Z0JBQ25CO29CQUNFLE9BQU8sSUFBSUEsTUFBTSxDQUFDLGVBQWUsRUFBRVAsTUFBTUssT0FBTyxFQUFFO1lBQ3REO1FBQ0Y7UUFFQSxPQUFPLElBQUlFLE1BQU0sR0FBR04sVUFBVSxTQUFTLEVBQUVELE1BQU1LLE9BQU8sSUFBSSxpQkFBaUI7SUFDN0U7SUFFQTs7R0FFQyxHQUNELE1BQWdCSSxNQUNkUixTQUEyQixFQUMzQlMsYUFBcUIsQ0FBQyxFQUN0QkMsUUFBZ0IsSUFBSSxFQUNSO1FBQ1osSUFBSUM7UUFFSixJQUFLLElBQUlDLFVBQVUsR0FBR0EsV0FBV0gsWUFBWUcsVUFBVztZQUN0RCxJQUFJO2dCQUNGLE9BQU8sTUFBTVo7WUFDZixFQUFFLE9BQU9ELE9BQU87Z0JBQ2RZLFlBQVlaO2dCQUVaLElBQUlhLFlBQVlILFlBQVk7b0JBQzFCLE1BQU1FO2dCQUNSO2dCQUVBLHNCQUFzQjtnQkFDdEIsTUFBTUUsV0FBV0gsUUFBUS9CLEtBQUtJLEdBQUcsQ0FBQyxHQUFHNkIsVUFBVTtnQkFDL0MsTUFBTSxJQUFJRSxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTRjtZQUNuRDtRQUNGO1FBRUEsTUFBTUY7SUFDUjtBQUNGIiwic291cmNlcyI6WyIvaG9tZS9jYXJkaW5hbHZpc2lvbi9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy96bXQtY2xvdWQvc3JjL2xpYi9jbG91ZC1zdG9yYWdlL2Jhc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ2xvdWRGaWxlLCBDbG91ZFByb3ZpZGVyLCBBdXRoVG9rZW5zIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG4vKipcbiAqIEJhc2UgYWJzdHJhY3QgY2xhc3MgZm9yIGNsb3VkIHN0b3JhZ2UgcHJvdmlkZXJzXG4gKi9cbmV4cG9ydCBhYnN0cmFjdCBjbGFzcyBDbG91ZFN0b3JhZ2VQcm92aWRlciB7XG4gIHByb3RlY3RlZCBwcm92aWRlcjogQ2xvdWRQcm92aWRlclsnaWQnXTtcbiAgcHJvdGVjdGVkIHRva2VuczogQXV0aFRva2VucyB8IG51bGwgPSBudWxsO1xuXG4gIGNvbnN0cnVjdG9yKHByb3ZpZGVyOiBDbG91ZFByb3ZpZGVyWydpZCddKSB7XG4gICAgdGhpcy5wcm92aWRlciA9IHByb3ZpZGVyO1xuICB9XG5cbiAgLyoqXG4gICAqIFNldCBhdXRoZW50aWNhdGlvbiB0b2tlbnNcbiAgICovXG4gIHNldFRva2Vucyh0b2tlbnM6IEF1dGhUb2tlbnMpOiB2b2lkIHtcbiAgICB0aGlzLnRva2VucyA9IHRva2VucztcbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgYXV0aGVudGljYXRpb24gdG9rZW5zXG4gICAqL1xuICBnZXRUb2tlbnMoKTogQXV0aFRva2VucyB8IG51bGwge1xuICAgIHJldHVybiB0aGlzLnRva2VucztcbiAgfVxuXG4gIC8qKlxuICAgKiBDaGVjayBpZiBwcm92aWRlciBpcyBhdXRoZW50aWNhdGVkXG4gICAqL1xuICBpc0F1dGhlbnRpY2F0ZWQoKTogYm9vbGVhbiB7XG4gICAgcmV0dXJuIHRoaXMudG9rZW5zICE9PSBudWxsICYmIHRoaXMudG9rZW5zLmFjY2Vzc1Rva2VuICE9PSAnJztcbiAgfVxuXG4gIC8qKlxuICAgKiBBYnN0cmFjdCBtZXRob2RzIHRoYXQgbXVzdCBiZSBpbXBsZW1lbnRlZCBieSBlYWNoIHByb3ZpZGVyXG4gICAqL1xuICBhYnN0cmFjdCBhdXRoZW50aWNhdGUoYXV0aENvZGU/OiBzdHJpbmcpOiBQcm9taXNlPEF1dGhUb2tlbnM+O1xuICBhYnN0cmFjdCByZWZyZXNoVG9rZW5zKCk6IFByb21pc2U8QXV0aFRva2Vucz47XG4gIGFic3RyYWN0IGdldFVzZXJJbmZvKCk6IFByb21pc2U8YW55PjtcbiAgYWJzdHJhY3QgbGlzdEZpbGVzKGZvbGRlcklkPzogc3RyaW5nLCBwYWdlVG9rZW4/OiBzdHJpbmcpOiBQcm9taXNlPHtcbiAgICBmaWxlczogQ2xvdWRGaWxlW107XG4gICAgbmV4dFBhZ2VUb2tlbj86IHN0cmluZztcbiAgfT47XG4gIGFic3RyYWN0IGRvd25sb2FkRmlsZShmaWxlSWQ6IHN0cmluZyk6IFByb21pc2U8QnVmZmVyPjtcbiAgYWJzdHJhY3QgdXBsb2FkRmlsZShcbiAgICBmaWxlbmFtZTogc3RyaW5nLCBcbiAgICBjb250ZW50OiBCdWZmZXIsIFxuICAgIGZvbGRlcklkPzogc3RyaW5nXG4gICk6IFByb21pc2U8Q2xvdWRGaWxlPjtcbiAgYWJzdHJhY3QgY3JlYXRlRm9sZGVyKG5hbWU6IHN0cmluZywgcGFyZW50SWQ/OiBzdHJpbmcpOiBQcm9taXNlPHN0cmluZz47XG4gIGFic3RyYWN0IGRlbGV0ZUZpbGUoZmlsZUlkOiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+O1xuICBhYnN0cmFjdCBnZXREb3dubG9hZFVybChmaWxlSWQ6IHN0cmluZyk6IFByb21pc2U8c3RyaW5nPjtcblxuICAvKipcbiAgICogR2V0IE9BdXRoIGF1dGhvcml6YXRpb24gVVJMXG4gICAqL1xuICBhYnN0cmFjdCBnZXRBdXRoVXJsKCk6IHN0cmluZztcblxuICAvKipcbiAgICogVmFsaWRhdGUgZmlsZSBiZWZvcmUgcHJvY2Vzc2luZ1xuICAgKi9cbiAgcHJvdGVjdGVkIHZhbGlkYXRlRmlsZShmaWxlOiBDbG91ZEZpbGUpOiBib29sZWFuIHtcbiAgICBpZiAoIWZpbGUuaWQgfHwgIWZpbGUubmFtZSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBcbiAgICBpZiAoZmlsZS5zaXplIDw9IDApIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG5cbiAgICByZXR1cm4gdHJ1ZTtcbiAgfVxuXG4gIC8qKlxuICAgKiBDb252ZXJ0IGZpbGUgc2l6ZSB0byBodW1hbiByZWFkYWJsZSBmb3JtYXRcbiAgICovXG4gIHByb3RlY3RlZCBmb3JtYXRGaWxlU2l6ZShieXRlczogbnVtYmVyKTogc3RyaW5nIHtcbiAgICBjb25zdCBzaXplcyA9IFsnQnl0ZXMnLCAnS0InLCAnTUInLCAnR0InLCAnVEInXTtcbiAgICBpZiAoYnl0ZXMgPT09IDApIHJldHVybiAnMCBCeXRlcyc7XG4gICAgXG4gICAgY29uc3QgaSA9IE1hdGguZmxvb3IoTWF0aC5sb2coYnl0ZXMpIC8gTWF0aC5sb2coMTAyNCkpO1xuICAgIHJldHVybiBNYXRoLnJvdW5kKGJ5dGVzIC8gTWF0aC5wb3coMTAyNCwgaSkgKiAxMDApIC8gMTAwICsgJyAnICsgc2l6ZXNbaV07XG4gIH1cblxuICAvKipcbiAgICogRXh0cmFjdCBmaWxlIGV4dGVuc2lvbiBmcm9tIGZpbGVuYW1lXG4gICAqL1xuICBwcm90ZWN0ZWQgZ2V0RmlsZUV4dGVuc2lvbihmaWxlbmFtZTogc3RyaW5nKTogc3RyaW5nIHtcbiAgICBjb25zdCBsYXN0RG90SW5kZXggPSBmaWxlbmFtZS5sYXN0SW5kZXhPZignLicpO1xuICAgIGlmIChsYXN0RG90SW5kZXggPT09IC0xKSByZXR1cm4gJyc7XG4gICAgcmV0dXJuIGZpbGVuYW1lLnN1YnN0cmluZyhsYXN0RG90SW5kZXgpLnRvTG93ZXJDYXNlKCk7XG4gIH1cblxuICAvKipcbiAgICogR2VuZXJhdGUgdW5pcXVlIGZpbGVuYW1lIGZvciBjb21wcmVzc2VkIGZpbGVcbiAgICovXG4gIHByb3RlY3RlZCBnZW5lcmF0ZUNvbXByZXNzZWRGaWxlbmFtZShvcmlnaW5hbEZpbGVuYW1lOiBzdHJpbmcpOiBzdHJpbmcge1xuICAgIGNvbnN0IGV4dGVuc2lvbiA9IHRoaXMuZ2V0RmlsZUV4dGVuc2lvbihvcmlnaW5hbEZpbGVuYW1lKTtcbiAgICBjb25zdCBuYW1lV2l0aG91dEV4dCA9IG9yaWdpbmFsRmlsZW5hbWUuc3Vic3RyaW5nKDAsIG9yaWdpbmFsRmlsZW5hbWUubGFzdEluZGV4T2YoJy4nKSk7XG4gICAgY29uc3QgdGltZXN0YW1wID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnJlcGxhY2UoL1s6Ll0vZywgJy0nKTtcbiAgICBcbiAgICByZXR1cm4gYCR7bmFtZVdpdGhvdXRFeHR9X2NvbXByZXNzZWRfJHt0aW1lc3RhbXB9JHtleHRlbnNpb259YDtcbiAgfVxuXG4gIC8qKlxuICAgKiBIYW5kbGUgQVBJIGVycm9ycyBjb25zaXN0ZW50bHlcbiAgICovXG4gIHByb3RlY3RlZCBoYW5kbGVFcnJvcihlcnJvcjogYW55LCBvcGVyYXRpb246IHN0cmluZyk6IEVycm9yIHtcbiAgICBjb25zb2xlLmVycm9yKGAke3RoaXMucHJvdmlkZXJ9ICR7b3BlcmF0aW9ufSBlcnJvcjpgLCBlcnJvcik7XG4gICAgXG4gICAgaWYgKGVycm9yLnJlc3BvbnNlKSB7XG4gICAgICBjb25zdCBzdGF0dXMgPSBlcnJvci5yZXNwb25zZS5zdGF0dXM7XG4gICAgICBjb25zdCBtZXNzYWdlID0gZXJyb3IucmVzcG9uc2UuZGF0YT8uZXJyb3I/Lm1lc3NhZ2UgfHwgZXJyb3IubWVzc2FnZTtcbiAgICAgIFxuICAgICAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICAgICAgY2FzZSA0MDE6XG4gICAgICAgICAgcmV0dXJuIG5ldyBFcnJvcihgQXV0aGVudGljYXRpb24gZmFpbGVkOiAke21lc3NhZ2V9YCk7XG4gICAgICAgIGNhc2UgNDAzOlxuICAgICAgICAgIHJldHVybiBuZXcgRXJyb3IoYEFjY2VzcyBkZW5pZWQ6ICR7bWVzc2FnZX1gKTtcbiAgICAgICAgY2FzZSA0MDQ6XG4gICAgICAgICAgcmV0dXJuIG5ldyBFcnJvcihgUmVzb3VyY2Ugbm90IGZvdW5kOiAke21lc3NhZ2V9YCk7XG4gICAgICAgIGNhc2UgNDI5OlxuICAgICAgICAgIHJldHVybiBuZXcgRXJyb3IoYFJhdGUgbGltaXQgZXhjZWVkZWQ6ICR7bWVzc2FnZX1gKTtcbiAgICAgICAgY2FzZSA1MDA6XG4gICAgICAgIGNhc2UgNTAyOlxuICAgICAgICBjYXNlIDUwMzpcbiAgICAgICAgICByZXR1cm4gbmV3IEVycm9yKGBTZXJ2ZXIgZXJyb3I6ICR7bWVzc2FnZX1gKTtcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICByZXR1cm4gbmV3IEVycm9yKGBBUEkgZXJyb3IgKCR7c3RhdHVzfSk6ICR7bWVzc2FnZX1gKTtcbiAgICAgIH1cbiAgICB9XG4gICAgXG4gICAgaWYgKGVycm9yLmNvZGUpIHtcbiAgICAgIHN3aXRjaCAoZXJyb3IuY29kZSkge1xuICAgICAgICBjYXNlICdFTk9URk9VTkQnOlxuICAgICAgICAgIHJldHVybiBuZXcgRXJyb3IoJ05ldHdvcmsgZXJyb3I6IFVuYWJsZSB0byBjb25uZWN0IHRvIHNlcnZpY2UnKTtcbiAgICAgICAgY2FzZSAnRVRJTUVET1VUJzpcbiAgICAgICAgICByZXR1cm4gbmV3IEVycm9yKCdOZXR3b3JrIGVycm9yOiBSZXF1ZXN0IHRpbWVvdXQnKTtcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICByZXR1cm4gbmV3IEVycm9yKGBOZXR3b3JrIGVycm9yOiAke2Vycm9yLm1lc3NhZ2V9YCk7XG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIHJldHVybiBuZXcgRXJyb3IoYCR7b3BlcmF0aW9ufSBmYWlsZWQ6ICR7ZXJyb3IubWVzc2FnZSB8fCAnVW5rbm93biBlcnJvcid9YCk7XG4gIH1cblxuICAvKipcbiAgICogUmV0cnkgbWVjaGFuaXNtIGZvciBBUEkgY2FsbHNcbiAgICovXG4gIHByb3RlY3RlZCBhc3luYyByZXRyeTxUPihcbiAgICBvcGVyYXRpb246ICgpID0+IFByb21pc2U8VD4sXG4gICAgbWF4UmV0cmllczogbnVtYmVyID0gMyxcbiAgICBkZWxheTogbnVtYmVyID0gMTAwMFxuICApOiBQcm9taXNlPFQ+IHtcbiAgICBsZXQgbGFzdEVycm9yOiBFcnJvcjtcbiAgICBcbiAgICBmb3IgKGxldCBhdHRlbXB0ID0gMTsgYXR0ZW1wdCA8PSBtYXhSZXRyaWVzOyBhdHRlbXB0KyspIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIHJldHVybiBhd2FpdCBvcGVyYXRpb24oKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGxhc3RFcnJvciA9IGVycm9yIGFzIEVycm9yO1xuICAgICAgICBcbiAgICAgICAgaWYgKGF0dGVtcHQgPT09IG1heFJldHJpZXMpIHtcbiAgICAgICAgICB0aHJvdyBsYXN0RXJyb3I7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC8vIEV4cG9uZW50aWFsIGJhY2tvZmZcbiAgICAgICAgY29uc3Qgd2FpdFRpbWUgPSBkZWxheSAqIE1hdGgucG93KDIsIGF0dGVtcHQgLSAxKTtcbiAgICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIHdhaXRUaW1lKSk7XG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIHRocm93IGxhc3RFcnJvciE7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJDbG91ZFN0b3JhZ2VQcm92aWRlciIsImNvbnN0cnVjdG9yIiwicHJvdmlkZXIiLCJ0b2tlbnMiLCJzZXRUb2tlbnMiLCJnZXRUb2tlbnMiLCJpc0F1dGhlbnRpY2F0ZWQiLCJhY2Nlc3NUb2tlbiIsInZhbGlkYXRlRmlsZSIsImZpbGUiLCJpZCIsIm5hbWUiLCJzaXplIiwiZm9ybWF0RmlsZVNpemUiLCJieXRlcyIsInNpemVzIiwiaSIsIk1hdGgiLCJmbG9vciIsImxvZyIsInJvdW5kIiwicG93IiwiZ2V0RmlsZUV4dGVuc2lvbiIsImZpbGVuYW1lIiwibGFzdERvdEluZGV4IiwibGFzdEluZGV4T2YiLCJzdWJzdHJpbmciLCJ0b0xvd2VyQ2FzZSIsImdlbmVyYXRlQ29tcHJlc3NlZEZpbGVuYW1lIiwib3JpZ2luYWxGaWxlbmFtZSIsImV4dGVuc2lvbiIsIm5hbWVXaXRob3V0RXh0IiwidGltZXN0YW1wIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwicmVwbGFjZSIsImhhbmRsZUVycm9yIiwiZXJyb3IiLCJvcGVyYXRpb24iLCJjb25zb2xlIiwicmVzcG9uc2UiLCJzdGF0dXMiLCJtZXNzYWdlIiwiZGF0YSIsIkVycm9yIiwiY29kZSIsInJldHJ5IiwibWF4UmV0cmllcyIsImRlbGF5IiwibGFzdEVycm9yIiwiYXR0ZW1wdCIsIndhaXRUaW1lIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/cloud-storage/base.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/cloud-storage/dropbox.ts":
/*!******************************************!*\
  !*** ./src/lib/cloud-storage/dropbox.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropboxProvider: () => (/* binding */ DropboxProvider)\n/* harmony export */ });\n/* harmony import */ var dropbox__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! dropbox */ \"dropbox\");\n/* harmony import */ var dropbox__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(dropbox__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _base__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base */ \"(rsc)/./src/lib/cloud-storage/base.ts\");\n\n\nclass DropboxProvider extends _base__WEBPACK_IMPORTED_MODULE_1__.CloudStorageProvider {\n    constructor(){\n        super('dropbox');\n        this.initializeClient();\n    }\n    initializeClient() {\n        this.dbx = new dropbox__WEBPACK_IMPORTED_MODULE_0__.Dropbox({\n            clientId: process.env.DROPBOX_CLIENT_ID,\n            clientSecret: process.env.DROPBOX_CLIENT_SECRET\n        });\n    }\n    getAuthUrl() {\n        const authUrl = this.dbx.getAuthenticationUrl(process.env.DROPBOX_REDIRECT_URI, undefined, 'code', 'offline');\n        return authUrl;\n    }\n    async authenticate(authCode) {\n        try {\n            const response = await this.dbx.getAccessTokenFromCode(process.env.DROPBOX_REDIRECT_URI, authCode);\n            const authTokens = {\n                accessToken: response.result.access_token,\n                refreshToken: response.result.refresh_token,\n                expiresAt: response.result.expires_in ? new Date(Date.now() + response.result.expires_in * 1000) : undefined\n            };\n            this.setTokens(authTokens);\n            this.dbx.setAccessToken(authTokens.accessToken);\n            return authTokens;\n        } catch (error) {\n            throw this.handleError(error, 'authentication');\n        }\n    }\n    async refreshTokens() {\n        try {\n            if (!this.tokens?.refreshToken) {\n                throw new Error('No refresh token available');\n            }\n            const response = await this.dbx.refreshAccessToken(this.tokens.refreshToken);\n            const authTokens = {\n                accessToken: response.result.access_token,\n                refreshToken: response.result.refresh_token || this.tokens.refreshToken,\n                expiresAt: response.result.expires_in ? new Date(Date.now() + response.result.expires_in * 1000) : undefined\n            };\n            this.setTokens(authTokens);\n            this.dbx.setAccessToken(authTokens.accessToken);\n            return authTokens;\n        } catch (error) {\n            throw this.handleError(error, 'token refresh');\n        }\n    }\n    async getUserInfo() {\n        try {\n            const response = await this.dbx.usersGetCurrentAccount();\n            const account = response.result;\n            return {\n                id: account.account_id,\n                name: account.name.display_name,\n                email: account.email,\n                avatar: account.profile_photo_url\n            };\n        } catch (error) {\n            throw this.handleError(error, 'get user info');\n        }\n    }\n    async listFiles(folderPath = '', cursor) {\n        try {\n            let response;\n            if (cursor) {\n                response = await this.dbx.filesListFolderContinue({\n                    cursor\n                });\n            } else {\n                response = await this.dbx.filesListFolder({\n                    path: folderPath || '',\n                    recursive: false,\n                    include_media_info: true,\n                    include_deleted: false\n                });\n            }\n            const files = response.result.entries.filter((entry)=>entry['.tag'] === 'file').map((file)=>({\n                    id: file.id,\n                    name: file.name,\n                    size: file.size,\n                    mimeType: this.getMimeTypeFromExtension(file.name),\n                    extension: this.getFileExtension(file.name),\n                    path: file.path_display,\n                    provider: this.provider,\n                    createdAt: new Date(file.client_modified),\n                    modifiedAt: new Date(file.server_modified)\n                }));\n            return {\n                files: files.filter((file)=>this.validateFile(file)),\n                nextPageToken: response.result.has_more ? response.result.cursor : undefined\n            };\n        } catch (error) {\n            throw this.handleError(error, 'list files');\n        }\n    }\n    async downloadFile(filePath) {\n        try {\n            const response = await this.dbx.filesDownload({\n                path: filePath\n            });\n            return Buffer.from(response.result.fileBinary);\n        } catch (error) {\n            throw this.handleError(error, 'download file');\n        }\n    }\n    async uploadFile(filename, content, folderPath = '') {\n        try {\n            const path = folderPath ? `${folderPath}/${filename}` : `/${filename}`;\n            const response = await this.dbx.filesUpload({\n                path,\n                contents: content,\n                mode: 'add',\n                autorename: true\n            });\n            const file = response.result;\n            return {\n                id: file.id,\n                name: file.name,\n                size: file.size,\n                mimeType: this.getMimeTypeFromExtension(file.name),\n                extension: this.getFileExtension(file.name),\n                path: file.path_display,\n                provider: this.provider,\n                createdAt: new Date(file.client_modified),\n                modifiedAt: new Date(file.server_modified)\n            };\n        } catch (error) {\n            throw this.handleError(error, 'upload file');\n        }\n    }\n    async createFolder(name, parentPath = '') {\n        try {\n            const path = parentPath ? `${parentPath}/${name}` : `/${name}`;\n            const response = await this.dbx.filesCreateFolderV2({\n                path,\n                autorename: false\n            });\n            return response.result.metadata.path_display;\n        } catch (error) {\n            throw this.handleError(error, 'create folder');\n        }\n    }\n    async deleteFile(filePath) {\n        try {\n            await this.dbx.filesDeleteV2({\n                path: filePath\n            });\n        } catch (error) {\n            throw this.handleError(error, 'delete file');\n        }\n    }\n    async getDownloadUrl(filePath) {\n        try {\n            const response = await this.dbx.filesGetTemporaryLink({\n                path: filePath\n            });\n            return response.result.link;\n        } catch (error) {\n            throw this.handleError(error, 'get download URL');\n        }\n    }\n    /**\n   * Find or create the Compressed folder\n   */ async getOrCreateCompressedFolder() {\n        try {\n            const compressedPath = '/Compressed';\n            try {\n                // Check if folder exists\n                await this.dbx.filesGetMetadata({\n                    path: compressedPath\n                });\n                return compressedPath;\n            } catch (error) {\n                if (error.status === 409) {\n                    // Folder doesn't exist, create it\n                    return await this.createFolder('Compressed');\n                }\n                throw error;\n            }\n        } catch (error) {\n            throw this.handleError(error, 'get or create compressed folder');\n        }\n    }\n    /**\n   * Get MIME type from file extension (Dropbox doesn't provide MIME types)\n   */ getMimeTypeFromExtension(filename) {\n        const extension = this.getFileExtension(filename);\n        const mimeTypes = {\n            '.txt': 'text/plain',\n            '.pdf': 'application/pdf',\n            '.doc': 'application/msword',\n            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n            '.xls': 'application/vnd.ms-excel',\n            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n            '.ppt': 'application/vnd.ms-powerpoint',\n            '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',\n            '.csv': 'text/csv',\n            '.mp4': 'video/mp4',\n            '.mkv': 'video/x-matroska',\n            '.avi': 'video/x-msvideo',\n            '.mov': 'video/quicktime',\n            '.webm': 'video/webm',\n            '.mp3': 'audio/mpeg',\n            '.aac': 'audio/aac',\n            '.opus': 'audio/opus',\n            '.m4a': 'audio/mp4',\n            '.tif': 'image/tiff',\n            '.tiff': 'image/tiff',\n            '.y4m': 'video/x-yuv4mpeg',\n            '.psd': 'image/vnd.adobe.photoshop',\n            '.db': 'application/x-sqlite3',\n            '.dcm': 'application/dicom'\n        };\n        return mimeTypes[extension] || 'application/octet-stream';\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/cloud-storage/dropbox.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/cloud-storage/factory.ts":
/*!******************************************!*\
  !*** ./src/lib/cloud-storage/factory.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CloudStorageFactory: () => (/* binding */ CloudStorageFactory),\n/* harmony export */   CloudStorageManager: () => (/* binding */ CloudStorageManager)\n/* harmony export */ });\n/* harmony import */ var _google_drive__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./google-drive */ \"(rsc)/./src/lib/cloud-storage/google-drive.ts\");\n/* harmony import */ var _dropbox__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./dropbox */ \"(rsc)/./src/lib/cloud-storage/dropbox.ts\");\n\n\n/**\n * Factory class for creating cloud storage providers\n */ class CloudStorageFactory {\n    static{\n        this.providers = new Map();\n    }\n    /**\n   * Get or create a cloud storage provider instance\n   */ static getProvider(providerId) {\n        if (!this.providers.has(providerId)) {\n            const provider = this.createProvider(providerId);\n            this.providers.set(providerId, provider);\n        }\n        return this.providers.get(providerId);\n    }\n    /**\n   * Create a new provider instance\n   */ static createProvider(providerId) {\n        switch(providerId){\n            case 'google-drive':\n                return new _google_drive__WEBPACK_IMPORTED_MODULE_0__.GoogleDriveProvider();\n            case 'dropbox':\n                return new _dropbox__WEBPACK_IMPORTED_MODULE_1__.DropboxProvider();\n            case 'icloud':\n                // iCloud implementation would go here\n                // For now, throw an error as it's not implemented\n                throw new Error('iCloud provider not yet implemented');\n            default:\n                throw new Error(`Unsupported cloud provider: ${providerId}`);\n        }\n    }\n    /**\n   * Get all available providers\n   */ static getAvailableProviders() {\n        return [\n            {\n                id: 'google-drive',\n                name: 'Google Drive',\n                icon: '/icons/google-drive.svg',\n                isConnected: false\n            },\n            {\n                id: 'dropbox',\n                name: 'Dropbox',\n                icon: '/icons/dropbox.svg',\n                isConnected: false\n            },\n            {\n                id: 'icloud',\n                name: 'iCloud Files',\n                icon: '/icons/icloud.svg',\n                isConnected: false\n            }\n        ];\n    }\n    /**\n   * Check if a provider is supported\n   */ static isProviderSupported(providerId) {\n        return [\n            'google-drive',\n            'dropbox',\n            'icloud'\n        ].includes(providerId);\n    }\n    /**\n   * Clear all provider instances (useful for testing)\n   */ static clearProviders() {\n        this.providers.clear();\n    }\n    /**\n   * Get provider configuration requirements\n   */ static getProviderConfig(providerId) {\n        switch(providerId){\n            case 'google-drive':\n                return {\n                    requiredEnvVars: [\n                        'GOOGLE_CLIENT_ID',\n                        'GOOGLE_CLIENT_SECRET',\n                        'GOOGLE_REDIRECT_URI'\n                    ],\n                    scopes: [\n                        'https://www.googleapis.com/auth/drive.file',\n                        'https://www.googleapis.com/auth/drive.readonly',\n                        'https://www.googleapis.com/auth/userinfo.profile'\n                    ],\n                    authType: 'oauth2'\n                };\n            case 'dropbox':\n                return {\n                    requiredEnvVars: [\n                        'DROPBOX_CLIENT_ID',\n                        'DROPBOX_CLIENT_SECRET',\n                        'DROPBOX_REDIRECT_URI'\n                    ],\n                    scopes: [\n                        'files.content.write',\n                        'files.content.read',\n                        'files.metadata.read'\n                    ],\n                    authType: 'oauth2'\n                };\n            case 'icloud':\n                return {\n                    requiredEnvVars: [\n                        'ICLOUD_CLIENT_ID',\n                        'ICLOUD_CLIENT_SECRET',\n                        'ICLOUD_REDIRECT_URI'\n                    ],\n                    scopes: [\n                        'cloudkit'\n                    ],\n                    authType: 'oauth2'\n                };\n            default:\n                throw new Error(`Unknown provider: ${providerId}`);\n        }\n    }\n    /**\n   * Validate provider configuration\n   */ static validateProviderConfig(providerId) {\n        const config = this.getProviderConfig(providerId);\n        const missingVars = [];\n        for (const envVar of config.requiredEnvVars){\n            if (!process.env[envVar]) {\n                missingVars.push(envVar);\n            }\n        }\n        return {\n            isValid: missingVars.length === 0,\n            missingVars\n        };\n    }\n}\n/**\n * Cloud Storage Manager - High-level interface for managing multiple providers\n */ class CloudStorageManager {\n    /**\n   * Connect to a cloud provider\n   */ async connectProvider(providerId, authCode) {\n        const provider = CloudStorageFactory.getProvider(providerId);\n        if (authCode) {\n            await provider.authenticate(authCode);\n        }\n        if (provider.isAuthenticated()) {\n            this.connectedProviders.set(providerId, provider);\n        }\n        return provider;\n    }\n    /**\n   * Disconnect from a cloud provider\n   */ disconnectProvider(providerId) {\n        this.connectedProviders.delete(providerId);\n    }\n    /**\n   * Get a connected provider\n   */ getConnectedProvider(providerId) {\n        return this.connectedProviders.get(providerId) || null;\n    }\n    /**\n   * Get all connected providers\n   */ getConnectedProviders() {\n        return Array.from(this.connectedProviders.keys());\n    }\n    /**\n   * Check if a provider is connected\n   */ isProviderConnected(providerId) {\n        const provider = this.connectedProviders.get(providerId);\n        return provider ? provider.isAuthenticated() : false;\n    }\n    /**\n   * Get provider status for all available providers\n   */ getProviderStatus() {\n        const providers = CloudStorageFactory.getAvailableProviders();\n        return providers.map((provider)=>({\n                ...provider,\n                isConnected: this.isProviderConnected(provider.id)\n            }));\n    }\n    /**\n   * Refresh tokens for all connected providers\n   */ async refreshAllTokens() {\n        const refreshPromises = Array.from(this.connectedProviders.values()).map((provider)=>provider.refreshTokens().catch((error)=>{\n                console.error('Failed to refresh tokens:', error);\n            }));\n        await Promise.all(refreshPromises);\n    }\n    constructor(){\n        this.connectedProviders = new Map();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/cloud-storage/factory.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/cloud-storage/google-drive.ts":
/*!***********************************************!*\
  !*** ./src/lib/cloud-storage/google-drive.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GoogleDriveProvider: () => (/* binding */ GoogleDriveProvider)\n/* harmony export */ });\n/* harmony import */ var googleapis__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! googleapis */ \"googleapis\");\n/* harmony import */ var googleapis__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(googleapis__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _base__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base */ \"(rsc)/./src/lib/cloud-storage/base.ts\");\n\n\nclass GoogleDriveProvider extends _base__WEBPACK_IMPORTED_MODULE_1__.CloudStorageProvider {\n    constructor(){\n        super('google-drive');\n        this.initializeClient();\n    }\n    initializeClient() {\n        this.oauth2Client = new googleapis__WEBPACK_IMPORTED_MODULE_0__.google.auth.OAuth2(process.env.GOOGLE_CLIENT_ID, process.env.GOOGLE_CLIENT_SECRET, process.env.GOOGLE_REDIRECT_URI);\n        this.drive = googleapis__WEBPACK_IMPORTED_MODULE_0__.google.drive({\n            version: 'v3',\n            auth: this.oauth2Client\n        });\n    }\n    /**\n   * Override setTokens to also update OAuth client credentials\n   */ setTokens(tokens) {\n        super.setTokens(tokens);\n        // Convert our token format to Google's format and set credentials\n        const expiryDate = tokens.expiresAt ? tokens.expiresAt instanceof Date ? tokens.expiresAt.getTime() : new Date(tokens.expiresAt).getTime() : undefined;\n        this.oauth2Client.setCredentials({\n            access_token: tokens.accessToken,\n            refresh_token: tokens.refreshToken,\n            expiry_date: expiryDate,\n            scope: tokens.scope?.join(' ')\n        });\n    }\n    getAuthUrl() {\n        const scopes = [\n            'https://www.googleapis.com/auth/drive.file',\n            'https://www.googleapis.com/auth/drive.readonly',\n            'https://www.googleapis.com/auth/userinfo.profile'\n        ];\n        return this.oauth2Client.generateAuthUrl({\n            access_type: 'offline',\n            scope: scopes,\n            prompt: 'consent'\n        });\n    }\n    async authenticate(authCode) {\n        try {\n            const { tokens } = await this.oauth2Client.getToken(authCode);\n            const authTokens = {\n                accessToken: tokens.access_token,\n                refreshToken: tokens.refresh_token,\n                expiresAt: tokens.expiry_date ? new Date(tokens.expiry_date) : undefined,\n                scope: tokens.scope?.split(' ')\n            };\n            this.setTokens(authTokens);\n            this.oauth2Client.setCredentials(tokens);\n            return authTokens;\n        } catch (error) {\n            throw this.handleError(error, 'authentication');\n        }\n    }\n    async refreshTokens() {\n        try {\n            if (!this.tokens?.refreshToken) {\n                throw new Error('No refresh token available');\n            }\n            this.oauth2Client.setCredentials({\n                refresh_token: this.tokens.refreshToken\n            });\n            const { credentials } = await this.oauth2Client.refreshAccessToken();\n            const authTokens = {\n                accessToken: credentials.access_token,\n                refreshToken: credentials.refresh_token || this.tokens.refreshToken,\n                expiresAt: credentials.expiry_date ? new Date(credentials.expiry_date) : undefined,\n                scope: credentials.scope?.split(' ')\n            };\n            this.setTokens(authTokens);\n            this.oauth2Client.setCredentials(credentials);\n            return authTokens;\n        } catch (error) {\n            throw this.handleError(error, 'token refresh');\n        }\n    }\n    async getUserInfo() {\n        try {\n            const oauth2 = googleapis__WEBPACK_IMPORTED_MODULE_0__.google.oauth2({\n                version: 'v2',\n                auth: this.oauth2Client\n            });\n            const { data } = await oauth2.userinfo.get();\n            return {\n                id: data.id,\n                name: data.name,\n                email: data.email,\n                avatar: data.picture\n            };\n        } catch (error) {\n            throw this.handleError(error, 'get user info');\n        }\n    }\n    async listFiles(folderId = 'root', pageToken) {\n        try {\n            const response = await this.drive.files.list({\n                q: `'${folderId}' in parents and trashed=false`,\n                fields: 'nextPageToken, files(id, name, size, mimeType, createdTime, modifiedTime, thumbnailLink, webContentLink)',\n                pageSize: 100,\n                pageToken\n            });\n            const files = response.data.files.map((file)=>({\n                    id: file.id,\n                    name: file.name,\n                    size: parseInt(file.size) || 0,\n                    mimeType: file.mimeType,\n                    extension: this.getFileExtension(file.name),\n                    path: `/${file.name}`,\n                    provider: this.provider,\n                    downloadUrl: file.webContentLink,\n                    thumbnailUrl: file.thumbnailLink,\n                    createdAt: new Date(file.createdTime),\n                    modifiedAt: new Date(file.modifiedTime)\n                }));\n            return {\n                files: files.filter((file)=>this.validateFile(file)),\n                nextPageToken: response.data.nextPageToken\n            };\n        } catch (error) {\n            throw this.handleError(error, 'list files');\n        }\n    }\n    async downloadFile(fileId) {\n        try {\n            const response = await this.drive.files.get({\n                fileId,\n                alt: 'media'\n            }, {\n                responseType: 'arraybuffer'\n            });\n            return Buffer.from(response.data);\n        } catch (error) {\n            throw this.handleError(error, 'download file');\n        }\n    }\n    async uploadFile(filename, content, folderId = 'root') {\n        try {\n            // Convert Buffer to stream for Google Drive API\n            const { Readable } = __webpack_require__(/*! stream */ \"stream\");\n            const stream = new Readable();\n            stream.push(content);\n            stream.push(null); // End the stream\n            const media = {\n                mimeType: 'application/octet-stream',\n                body: stream\n            };\n            const fileMetadata = {\n                name: filename,\n                parents: [\n                    folderId\n                ]\n            };\n            const response = await this.drive.files.create({\n                resource: fileMetadata,\n                media,\n                fields: 'id, name, size, mimeType, createdTime, modifiedTime'\n            });\n            const file = response.data;\n            return {\n                id: file.id,\n                name: file.name,\n                size: parseInt(file.size) || content.length,\n                mimeType: file.mimeType,\n                extension: this.getFileExtension(file.name),\n                path: `/${file.name}`,\n                provider: this.provider,\n                createdAt: new Date(file.createdTime),\n                modifiedAt: new Date(file.modifiedTime)\n            };\n        } catch (error) {\n            throw this.handleError(error, 'upload file');\n        }\n    }\n    async createFolder(name, parentId = 'root') {\n        try {\n            const fileMetadata = {\n                name,\n                mimeType: 'application/vnd.google-apps.folder',\n                parents: [\n                    parentId\n                ]\n            };\n            const response = await this.drive.files.create({\n                resource: fileMetadata,\n                fields: 'id'\n            });\n            return response.data.id;\n        } catch (error) {\n            throw this.handleError(error, 'create folder');\n        }\n    }\n    async deleteFile(fileId) {\n        try {\n            await this.drive.files.delete({\n                fileId\n            });\n        } catch (error) {\n            throw this.handleError(error, 'delete file');\n        }\n    }\n    async getDownloadUrl(fileId) {\n        try {\n            const response = await this.drive.files.get({\n                fileId,\n                fields: 'webContentLink'\n            });\n            return response.data.webContentLink;\n        } catch (error) {\n            throw this.handleError(error, 'get download URL');\n        }\n    }\n    /**\n   * Find or create the Compressed folder\n   */ async getOrCreateCompressedFolder() {\n        try {\n            // First, try to find existing Compressed folder\n            const response = await this.drive.files.list({\n                q: \"name='Compressed' and mimeType='application/vnd.google-apps.folder' and trashed=false\",\n                fields: 'files(id, name)'\n            });\n            if (response.data.files && response.data.files.length > 0) {\n                return response.data.files[0].id;\n            }\n            // Create new Compressed folder if it doesn't exist\n            return await this.createFolder('Compressed');\n        } catch (error) {\n            throw this.handleError(error, 'get or create compressed folder');\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/cloud-storage/google-drive.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "dropbox":
/*!**************************!*\
  !*** external "dropbox" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("dropbox");

/***/ }),

/***/ "googleapis":
/*!*****************************!*\
  !*** external "googleapis" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("googleapis");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5Bprovider%5D%2Froute&page=%2Fapi%2Fauth%2F%5Bprovider%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5Bprovider%5D%2Froute.ts&appDir=%2Fhome%2Fcardinalvision%2FDocuments%2Faugment-projects%2Fzmt-cloud%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fcardinalvision%2FDocuments%2Faugment-projects%2Fzmt-cloud&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();