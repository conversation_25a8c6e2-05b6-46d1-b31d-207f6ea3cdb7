/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fcardinalvision%2FDocuments%2Faugment-projects%2Fzmt-cloud%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fcardinalvision%2FDocuments%2Faugment-projects%2Fzmt-cloud%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(app-pages-browser)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGaG9tZSUyRmNhcmRpbmFsdmlzaW9uJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRnptdC1jbG91ZCUyRnNyYyUyRmFwcCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQStHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS9jYXJkaW5hbHZpc2lvbi9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy96bXQtY2xvdWQvc3JjL2FwcC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fcardinalvision%2FDocuments%2Faugment-projects%2Fzmt-cloud%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/path-browserify/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/path-browserify/index.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(function(){\"use strict\";var e={114:function(e){function assertPath(e){if(typeof e!==\"string\"){throw new TypeError(\"Path must be a string. Received \"+JSON.stringify(e))}}function normalizeStringPosix(e,r){var t=\"\";var i=0;var n=-1;var a=0;var f;for(var l=0;l<=e.length;++l){if(l<e.length)f=e.charCodeAt(l);else if(f===47)break;else f=47;if(f===47){if(n===l-1||a===1){}else if(n!==l-1&&a===2){if(t.length<2||i!==2||t.charCodeAt(t.length-1)!==46||t.charCodeAt(t.length-2)!==46){if(t.length>2){var s=t.lastIndexOf(\"/\");if(s!==t.length-1){if(s===-1){t=\"\";i=0}else{t=t.slice(0,s);i=t.length-1-t.lastIndexOf(\"/\")}n=l;a=0;continue}}else if(t.length===2||t.length===1){t=\"\";i=0;n=l;a=0;continue}}if(r){if(t.length>0)t+=\"/..\";else t=\"..\";i=2}}else{if(t.length>0)t+=\"/\"+e.slice(n+1,l);else t=e.slice(n+1,l);i=l-n-1}n=l;a=0}else if(f===46&&a!==-1){++a}else{a=-1}}return t}function _format(e,r){var t=r.dir||r.root;var i=r.base||(r.name||\"\")+(r.ext||\"\");if(!t){return i}if(t===r.root){return t+i}return t+e+i}var r={resolve:function resolve(){var e=\"\";var r=false;var t;for(var i=arguments.length-1;i>=-1&&!r;i--){var n;if(i>=0)n=arguments[i];else{if(t===undefined)t=\"\";n=t}assertPath(n);if(n.length===0){continue}e=n+\"/\"+e;r=n.charCodeAt(0)===47}e=normalizeStringPosix(e,!r);if(r){if(e.length>0)return\"/\"+e;else return\"/\"}else if(e.length>0){return e}else{return\".\"}},normalize:function normalize(e){assertPath(e);if(e.length===0)return\".\";var r=e.charCodeAt(0)===47;var t=e.charCodeAt(e.length-1)===47;e=normalizeStringPosix(e,!r);if(e.length===0&&!r)e=\".\";if(e.length>0&&t)e+=\"/\";if(r)return\"/\"+e;return e},isAbsolute:function isAbsolute(e){assertPath(e);return e.length>0&&e.charCodeAt(0)===47},join:function join(){if(arguments.length===0)return\".\";var e;for(var t=0;t<arguments.length;++t){var i=arguments[t];assertPath(i);if(i.length>0){if(e===undefined)e=i;else e+=\"/\"+i}}if(e===undefined)return\".\";return r.normalize(e)},relative:function relative(e,t){assertPath(e);assertPath(t);if(e===t)return\"\";e=r.resolve(e);t=r.resolve(t);if(e===t)return\"\";var i=1;for(;i<e.length;++i){if(e.charCodeAt(i)!==47)break}var n=e.length;var a=n-i;var f=1;for(;f<t.length;++f){if(t.charCodeAt(f)!==47)break}var l=t.length;var s=l-f;var o=a<s?a:s;var u=-1;var h=0;for(;h<=o;++h){if(h===o){if(s>o){if(t.charCodeAt(f+h)===47){return t.slice(f+h+1)}else if(h===0){return t.slice(f+h)}}else if(a>o){if(e.charCodeAt(i+h)===47){u=h}else if(h===0){u=0}}break}var c=e.charCodeAt(i+h);var v=t.charCodeAt(f+h);if(c!==v)break;else if(c===47)u=h}var g=\"\";for(h=i+u+1;h<=n;++h){if(h===n||e.charCodeAt(h)===47){if(g.length===0)g+=\"..\";else g+=\"/..\"}}if(g.length>0)return g+t.slice(f+u);else{f+=u;if(t.charCodeAt(f)===47)++f;return t.slice(f)}},_makeLong:function _makeLong(e){return e},dirname:function dirname(e){assertPath(e);if(e.length===0)return\".\";var r=e.charCodeAt(0);var t=r===47;var i=-1;var n=true;for(var a=e.length-1;a>=1;--a){r=e.charCodeAt(a);if(r===47){if(!n){i=a;break}}else{n=false}}if(i===-1)return t?\"/\":\".\";if(t&&i===1)return\"//\";return e.slice(0,i)},basename:function basename(e,r){if(r!==undefined&&typeof r!==\"string\")throw new TypeError('\"ext\" argument must be a string');assertPath(e);var t=0;var i=-1;var n=true;var a;if(r!==undefined&&r.length>0&&r.length<=e.length){if(r.length===e.length&&r===e)return\"\";var f=r.length-1;var l=-1;for(a=e.length-1;a>=0;--a){var s=e.charCodeAt(a);if(s===47){if(!n){t=a+1;break}}else{if(l===-1){n=false;l=a+1}if(f>=0){if(s===r.charCodeAt(f)){if(--f===-1){i=a}}else{f=-1;i=l}}}}if(t===i)i=l;else if(i===-1)i=e.length;return e.slice(t,i)}else{for(a=e.length-1;a>=0;--a){if(e.charCodeAt(a)===47){if(!n){t=a+1;break}}else if(i===-1){n=false;i=a+1}}if(i===-1)return\"\";return e.slice(t,i)}},extname:function extname(e){assertPath(e);var r=-1;var t=0;var i=-1;var n=true;var a=0;for(var f=e.length-1;f>=0;--f){var l=e.charCodeAt(f);if(l===47){if(!n){t=f+1;break}continue}if(i===-1){n=false;i=f+1}if(l===46){if(r===-1)r=f;else if(a!==1)a=1}else if(r!==-1){a=-1}}if(r===-1||i===-1||a===0||a===1&&r===i-1&&r===t+1){return\"\"}return e.slice(r,i)},format:function format(e){if(e===null||typeof e!==\"object\"){throw new TypeError('The \"pathObject\" argument must be of type Object. Received type '+typeof e)}return _format(\"/\",e)},parse:function parse(e){assertPath(e);var r={root:\"\",dir:\"\",base:\"\",ext:\"\",name:\"\"};if(e.length===0)return r;var t=e.charCodeAt(0);var i=t===47;var n;if(i){r.root=\"/\";n=1}else{n=0}var a=-1;var f=0;var l=-1;var s=true;var o=e.length-1;var u=0;for(;o>=n;--o){t=e.charCodeAt(o);if(t===47){if(!s){f=o+1;break}continue}if(l===-1){s=false;l=o+1}if(t===46){if(a===-1)a=o;else if(u!==1)u=1}else if(a!==-1){u=-1}}if(a===-1||l===-1||u===0||u===1&&a===l-1&&a===f+1){if(l!==-1){if(f===0&&i)r.base=r.name=e.slice(1,l);else r.base=r.name=e.slice(f,l)}}else{if(f===0&&i){r.name=e.slice(1,a);r.base=e.slice(1,l)}else{r.name=e.slice(f,a);r.base=e.slice(f,l)}r.ext=e.slice(a,l)}if(f>0)r.dir=e.slice(0,f-1);else if(i)r.dir=\"/\";return r},sep:\"/\",delimiter:\":\",win32:null,posix:null};r.posix=r;e.exports=r}};var r={};function __nccwpck_require__(t){var i=r[t];if(i!==undefined){return i.exports}var n=r[t]={exports:{}};var a=true;try{e[t](n,n.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return n.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(114);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/path-browserify/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIi9ob21lL2NhcmRpbmFsdmlzaW9uL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL3ptdC1jbG91ZC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0L2pzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_CloudProviderCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/CloudProviderCard */ \"(app-pages-browser)/./src/components/CloudProviderCard.tsx\");\n/* harmony import */ var _components_FileList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/FileList */ \"(app-pages-browser)/./src/components/FileList.tsx\");\n/* harmony import */ var _components_CompressionPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/CompressionPanel */ \"(app-pages-browser)/./src/components/CompressionPanel.tsx\");\n/* harmony import */ var _components_JobStatus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/JobStatus */ \"(app-pages-browser)/./src/components/JobStatus.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// Remove client-side import of cloud storage - will use API routes instead\n\n\n\n\nfunction Home() {\n    _s();\n    const [providers, setProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedFiles, setSelectedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentJob, setCurrentJob] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connectedProviders, setConnectedProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            // Initialize providers - hardcoded for client-side to avoid server imports\n            const availableProviders = [\n                {\n                    id: 'google-drive',\n                    name: 'Google Drive',\n                    icon: '/icons/google-drive.svg',\n                    isConnected: false\n                },\n                {\n                    id: 'dropbox',\n                    name: 'Dropbox',\n                    icon: '/icons/dropbox.svg',\n                    isConnected: false\n                },\n                {\n                    id: 'icloud',\n                    name: 'iCloud Files',\n                    icon: '/icons/icloud.svg',\n                    isConnected: false\n                }\n            ];\n            setProviders(availableProviders);\n        }\n    }[\"Home.useEffect\"], []);\n    const handleProviderConnect = async (providerId)=>{\n        try {\n            // Get auth URL\n            const response = await fetch(\"/api/auth/\".concat(providerId));\n            const { authUrl } = await response.json();\n            // Open auth window\n            const authWindow = window.open(authUrl, 'auth', 'width=600,height=600');\n            // Listen for auth completion\n            const handleMessage = async (event)=>{\n                if (event.data.type === 'auth-success' && event.data.provider === providerId) {\n                    try {\n                        // Exchange code for tokens\n                        const tokenResponse = await fetch(\"/api/auth/\".concat(providerId), {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                code: event.data.code\n                            })\n                        });\n                        const { tokens, userInfo } = await tokenResponse.json();\n                        // Store tokens securely (in production, use secure storage)\n                        const existingTokens = JSON.parse(localStorage.getItem('cloudTokens') || '{}');\n                        existingTokens[providerId] = tokens;\n                        localStorage.setItem('cloudTokens', JSON.stringify(existingTokens));\n                        // Update UI state\n                        setConnectedProviders((prev)=>new Set([\n                                ...prev,\n                                providerId\n                            ]));\n                        setProviders((prev)=>prev.map((p)=>p.id === providerId ? {\n                                    ...p,\n                                    isConnected: true\n                                } : p));\n                        authWindow === null || authWindow === void 0 ? void 0 : authWindow.close();\n                        window.removeEventListener('message', handleMessage);\n                        console.log(\"Successfully connected to \".concat(providerId, \":\"), userInfo);\n                    } catch (error) {\n                        console.error('Failed to exchange code for tokens:', error);\n                    }\n                }\n            };\n            window.addEventListener('message', handleMessage);\n        } catch (error) {\n            console.error('Failed to connect provider:', error);\n        }\n    };\n    const handleFileSelect = (files)=>{\n        setSelectedFiles(files);\n    };\n    const handleStartCompression = async ()=>{\n        if (selectedFiles.length === 0) return;\n        try {\n            // Get stored tokens (in a real app, these would be securely stored)\n            const tokens = JSON.parse(localStorage.getItem('cloudTokens') || '{}');\n            const response = await fetch('/api/compression/jobs', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    files: selectedFiles,\n                    tokens\n                })\n            });\n            const { data: job } = await response.json();\n            setCurrentJob(job);\n            // Start polling for job updates\n            pollJobStatus(job.id);\n        } catch (error) {\n            console.error('Failed to start compression:', error);\n        }\n    };\n    const pollJobStatus = (jobId)=>{\n        const interval = setInterval(async ()=>{\n            try {\n                const response = await fetch(\"/api/compression/jobs?jobId=\".concat(jobId));\n                const { data: job } = await response.json();\n                setCurrentJob(job);\n                if (job.status === 'completed' || job.status === 'failed') {\n                    clearInterval(interval);\n                }\n            } catch (error) {\n                console.error('Failed to poll job status:', error);\n                clearInterval(interval);\n            }\n        }, 2000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"CloudZMT\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-3 text-sm text-gray-500\",\n                                        children: \"Multi-Cloud File Compression\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        connectedProviders.size,\n                                        \" provider\",\n                                        connectedProviders.size !== 1 ? 's' : '',\n                                        \" connected\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Cloud Storage Providers\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: providers.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CloudProviderCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                provider: provider,\n                                                onConnect: ()=>handleProviderConnect(provider.id)\n                                            }, provider.id, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FileList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            connectedProviders: Array.from(connectedProviders),\n                                            onFileSelect: handleFileSelect,\n                                            selectedFiles: selectedFiles\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    selectedFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CompressionPanel__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            selectedFiles: selectedFiles,\n                                            onStartCompression: handleStartCompression,\n                                            isProcessing: (currentJob === null || currentJob === void 0 ? void 0 : currentJob.status) === 'processing'\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 17\n                                    }, this),\n                                    currentJob && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_JobStatus__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            job: currentJob\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/app/page.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"iMnFloo9ECYNQbI4mvsnCuI4uaM=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/CloudProviderCard.tsx":
/*!**********************************************!*\
  !*** ./src/components/CloudProviderCard.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CloudProviderCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction CloudProviderCard(param) {\n    let { provider, onConnect } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 bg-gray-400 rounded\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CloudProviderCard.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CloudProviderCard.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-gray-900\",\n                                        children: provider.name\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CloudProviderCard.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: provider.isConnected ? 'Connected' : 'Not connected'\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CloudProviderCard.tsx\",\n                                        lineNumber: 21,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CloudProviderCard.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CloudProviderCard.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: provider.isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CloudProviderCard.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-green-600 font-medium\",\n                                    children: \"Connected\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CloudProviderCard.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CloudProviderCard.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onConnect,\n                            className: \"px-3 py-1.5 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 transition-colors\",\n                            children: \"Connect\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CloudProviderCard.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CloudProviderCard.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CloudProviderCard.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            provider.isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3 pt-3 border-t border-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500\",\n                            children: \"Status\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CloudProviderCard.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-600 font-medium\",\n                            children: \"Active\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CloudProviderCard.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CloudProviderCard.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CloudProviderCard.tsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CloudProviderCard.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n_c = CloudProviderCard;\nvar _c;\n$RefreshReg$(_c, \"CloudProviderCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CloudProviderCard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/CompressionPanel.tsx":
/*!*********************************************!*\
  !*** ./src/components/CompressionPanel.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CompressionPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_compression_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/compression-config */ \"(app-pages-browser)/./src/lib/compression-config.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction CompressionPanel(param) {\n    let { selectedFiles, onStartCompression, isProcessing } = param;\n    const formatFileSize = (bytes)=>{\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        if (bytes === 0) return '0 Bytes';\n        const i = Math.floor(Math.log(bytes) / Math.log(1024));\n        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n    };\n    const totalSize = selectedFiles.reduce((sum, file)=>sum + file.size, 0);\n    // Calculate estimated compression statistics\n    const compressionStats = selectedFiles.reduce((stats, file)=>{\n        const config = (0,_lib_compression_config__WEBPACK_IMPORTED_MODULE_1__.getCompressionConfig)(file.name);\n        if (config) {\n            const ratio = (0,_lib_compression_config__WEBPACK_IMPORTED_MODULE_1__.estimateCompressionRatio)(file.name);\n            stats.estimatedCompressedSize += file.size * ratio;\n            stats.filesByType[config.description] = (stats.filesByType[config.description] || 0) + 1;\n        }\n        return stats;\n    }, {\n        estimatedCompressedSize: 0,\n        filesByType: {}\n    });\n    const estimatedSavings = totalSize - compressionStats.estimatedCompressedSize;\n    const savingsPercentage = totalSize > 0 ? estimatedSavings / totalSize * 100 : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                children: \"Compression Settings\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-medium text-gray-900 mb-3\",\n                        children: \"Selected Files Summary\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Total Files\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: selectedFiles.length\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Total Size\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: formatFileSize(totalSize)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-2\",\n                                children: \"File Types\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: Object.entries(compressionStats.filesByType).map((param)=>{\n                                    let [type, count] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-700\",\n                                                children: type\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-900 font-medium\",\n                                                children: [\n                                                    count,\n                                                    \" file\",\n                                                    count !== 1 ? 's' : ''\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, type, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-medium text-blue-900 mb-3\",\n                        children: \"Estimated Compression Results\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-700\",\n                                        children: \"Estimated Compressed Size\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold text-blue-900\",\n                                        children: formatFileSize(compressionStats.estimatedCompressedSize)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-700\",\n                                        children: \"Estimated Savings\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold text-blue-900\",\n                                        children: [\n                                            formatFileSize(estimatedSavings),\n                                            \" (\",\n                                            savingsPercentage.toFixed(1),\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 pt-3 border-t border-blue-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-blue-600\",\n                            children: \"* Estimates are based on typical compression ratios for each file type. Actual results may vary depending on file content and compression settings.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-medium text-gray-900 mb-3\",\n                        children: \"Compression Options\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        id: \"create-folder\",\n                                        type: \"checkbox\",\n                                        defaultChecked: true,\n                                        className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"create-folder\",\n                                        className: \"ml-2 text-sm text-gray-700\",\n                                        children: 'Create \"Compressed\" folder in cloud storage'\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        id: \"preserve-structure\",\n                                        type: \"checkbox\",\n                                        defaultChecked: true,\n                                        className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"preserve-structure\",\n                                        className: \"ml-2 text-sm text-gray-700\",\n                                        children: \"Preserve original file structure\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        id: \"delete-originals\",\n                                        type: \"checkbox\",\n                                        defaultChecked: false,\n                                        className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"delete-originals\",\n                                        className: \"ml-2 text-sm text-gray-700\",\n                                        children: \"Delete original files after compression\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            \"Ready to compress \",\n                            selectedFiles.length,\n                            \" file\",\n                            selectedFiles.length !== 1 ? 's' : ''\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onStartCompression,\n                        disabled: isProcessing || selectedFiles.length === 0,\n                        className: \"px-6 py-2 rounded-md font-medium transition-colors \".concat(isProcessing || selectedFiles.length === 0 ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'bg-blue-600 text-white hover:bg-blue-700'),\n                        children: isProcessing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Processing...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this) : 'Start Compression'\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            totalSize > 100 * 1024 * 1024 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-5 w-5 text-yellow-400\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-yellow-800\",\n                                    children: \"Large File Warning\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-yellow-700 mt-1\",\n                                    children: [\n                                        \"You're compressing \",\n                                        formatFileSize(totalSize),\n                                        \" of data. This may take several minutes to complete.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n                lineNumber: 175,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/CompressionPanel.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_c = CompressionPanel;\nvar _c;\n$RefreshReg$(_c, \"CompressionPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CompressionPanel.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/FileList.tsx":
/*!*************************************!*\
  !*** ./src/components/FileList.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FileList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_compression_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/compression-config */ \"(app-pages-browser)/./src/lib/compression-config.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction FileList(param) {\n    let { connectedProviders, onFileSelect, selectedFiles } = param;\n    _s();\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProvider, setSelectedProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FileList.useEffect\": ()=>{\n            if (selectedProvider && connectedProviders.includes(selectedProvider)) {\n                loadFiles(selectedProvider);\n            }\n        }\n    }[\"FileList.useEffect\"], [\n        selectedProvider,\n        connectedProviders\n    ]);\n    const loadFiles = async (providerId)=>{\n        setLoading(true);\n        setError('');\n        try {\n            // Get stored tokens\n            const tokens = JSON.parse(localStorage.getItem('cloudTokens') || '{}');\n            const providerTokens = tokens[providerId];\n            if (!providerTokens) {\n                throw new Error('No authentication tokens found');\n            }\n            const response = await fetch(\"/api/files/\".concat(providerId), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(JSON.stringify(providerTokens))\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Failed to load files');\n            }\n            const { data } = await response.json();\n            setFiles(data.files || []);\n        } catch (error) {\n            console.error('Failed to load files:', error);\n            setError(error instanceof Error ? error.message : 'Failed to load files');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleFileToggle = (file)=>{\n        const isSelected = selectedFiles.some((f)=>f.id === file.id);\n        let newSelection;\n        if (isSelected) {\n            newSelection = selectedFiles.filter((f)=>f.id !== file.id);\n        } else {\n            newSelection = [\n                ...selectedFiles,\n                file\n            ];\n        }\n        onFileSelect(newSelection);\n    };\n    const handleSelectAll = ()=>{\n        const supportedFiles = files.filter((file)=>(0,_lib_compression_config__WEBPACK_IMPORTED_MODULE_2__.isFileSupported)(file.name));\n        onFileSelect(supportedFiles);\n    };\n    const handleClearSelection = ()=>{\n        onFileSelect([]);\n    };\n    const formatFileSize = (bytes)=>{\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        if (bytes === 0) return '0 Bytes';\n        const i = Math.floor(Math.log(bytes) / Math.log(1024));\n        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n    };\n    const supportedFiles = files.filter((file)=>(0,_lib_compression_config__WEBPACK_IMPORTED_MODULE_2__.isFileSupported)(file.name));\n    const supportedExtensions = (0,_lib_compression_config__WEBPACK_IMPORTED_MODULE_2__.getSupportedExtensions)();\n    if (connectedProviders.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                    children: \"File Selection\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-400 mb-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"mx-auto h-12 w-12\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-1\",\n                            children: \"No Cloud Providers Connected\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500\",\n                            children: \"Connect to a cloud storage provider to browse and select files for compression.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"File Selection\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    selectedFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    selectedFiles.length,\n                                    \" selected\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClearSelection,\n                                className: \"text-sm text-red-600 hover:text-red-700\",\n                                children: \"Clear\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                        children: \"Select Cloud Provider\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: selectedProvider,\n                        onChange: (e)=>setSelectedProvider(e.target.value),\n                        className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                children: \"Choose a provider...\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this),\n                            connectedProviders.map((providerId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: providerId,\n                                    children: providerId.charAt(0).toUpperCase() + providerId.slice(1).replace('-', ' ')\n                                }, providerId, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-blue-900 mb-1\",\n                        children: \"Supported File Types\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-blue-700\",\n                        children: supportedExtensions.join(', ')\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 mt-2\",\n                        children: \"Loading files...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-red-50 border border-red-200 rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-700\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, this),\n            !loading && !error && files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    supportedFiles.length,\n                                    \" of \",\n                                    files.length,\n                                    \" files supported\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this),\n                            supportedFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSelectAll,\n                                className: \"text-sm text-blue-600 hover:text-blue-700\",\n                                children: \"Select All Supported\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border border-gray-200 rounded-md divide-y divide-gray-200 max-h-96 overflow-y-auto\",\n                        children: files.map((file)=>{\n                            const isSupported = (0,_lib_compression_config__WEBPACK_IMPORTED_MODULE_2__.isFileSupported)(file.name);\n                            const isSelected = selectedFiles.some((f)=>f.id === file.id);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 flex items-center justify-between \".concat(isSupported ? 'hover:bg-gray-50 cursor-pointer' : 'bg-gray-50 cursor-not-allowed opacity-60', \" \").concat(isSelected ? 'bg-blue-50 border-blue-200' : ''),\n                                onClick: ()=>isSupported && handleFileToggle(file),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            isSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: isSelected,\n                                                onChange: ()=>handleFileToggle(file),\n                                                className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: file.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            formatFileSize(file.size),\n                                                            \" • \",\n                                                            file.extension,\n                                                            !isSupported && ' • Not supported'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: new Date(file.modifiedAt).toLocaleDateString()\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, file.id, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this),\n            !loading && !error && selectedProvider && files.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-400 mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"mx-auto h-12 w-12\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            stroke: \"currentColor\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-1\",\n                        children: \"No Files Found\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"This folder appears to be empty or you may not have access to view files.\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n                lineNumber: 227,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/FileList.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, this);\n}\n_s(FileList, \"MIN98MgqtxwEYeER3548j3K6TD4=\");\n_c = FileList;\nvar _c;\n$RefreshReg$(_c, \"FileList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FileList.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/JobStatus.tsx":
/*!**************************************!*\
  !*** ./src/components/JobStatus.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ JobStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction JobStatus(param) {\n    let { job } = param;\n    const formatFileSize = (bytes)=>{\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        if (bytes === 0) return '0 Bytes';\n        const i = Math.floor(Math.log(bytes) / Math.log(1024));\n        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n    };\n    const formatDuration = (ms)=>{\n        const seconds = Math.floor(ms / 1000);\n        const minutes = Math.floor(seconds / 60);\n        const hours = Math.floor(minutes / 60);\n        if (hours > 0) {\n            return \"\".concat(hours, \"h \").concat(minutes % 60, \"m \").concat(seconds % 60, \"s\");\n        } else if (minutes > 0) {\n            return \"\".concat(minutes, \"m \").concat(seconds % 60, \"s\");\n        } else {\n            return \"\".concat(seconds, \"s\");\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'pending':\n                return 'text-yellow-600 bg-yellow-100';\n            case 'processing':\n                return 'text-blue-600 bg-blue-100';\n            case 'completed':\n                return 'text-green-600 bg-green-100';\n            case 'failed':\n                return 'text-red-600 bg-red-100';\n            default:\n                return 'text-gray-600 bg-gray-100';\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'pending':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 11\n                }, this);\n            case 'processing':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-current\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 11\n                }, this);\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M5 13l4 4L19 7\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 11\n                }, this);\n            case 'failed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M6 18L18 6M6 6l12 12\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Compression Job Status\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getStatusColor(job.status)),\n                        children: [\n                            getStatusIcon(job.status),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-1 capitalize\",\n                                children: job.status\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Job ID\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-mono text-gray-900\",\n                                    children: [\n                                        job.id.slice(0, 8),\n                                        \"...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Files\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-semibold text-gray-900\",\n                                    children: job.files.length\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Original Size\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-semibold text-gray-900\",\n                                    children: formatFileSize(job.originalSize)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Started\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-900\",\n                                    children: new Date(job.startTime).toLocaleTimeString()\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            job.status === 'processing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between text-sm text-gray-600 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Progress\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    job.progress,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                            style: {\n                                width: \"\".concat(job.progress, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, this),\n            job.status === 'completed' && job.compressedSize !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-medium text-green-900 mb-3\",\n                        children: \"Compression Results\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-green-700\",\n                                        children: \"Compressed Size\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold text-green-900\",\n                                        children: formatFileSize(job.compressedSize)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-green-700\",\n                                        children: \"Space Saved\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold text-green-900\",\n                                        children: formatFileSize(job.originalSize - job.compressedSize)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-green-700\",\n                                        children: \"Compression Ratio\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold text-green-900\",\n                                        children: [\n                                            ((job.compressionRatio || 1) * 100).toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this),\n                    job.duration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 pt-3 border-t border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-green-700\",\n                            children: [\n                                \"Completed in \",\n                                formatDuration(job.duration)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, this),\n            job.status === 'failed' && job.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-medium text-red-900 mb-2\",\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-700\",\n                        children: job.error\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, this),\n            job.outputFiles && job.outputFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-medium text-gray-900 mb-3\",\n                        children: \"Compressed Files\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border border-gray-200 rounded-md divide-y divide-gray-200\",\n                        children: job.outputFiles.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: file.name\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: formatFileSize(file.size)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-sm text-blue-600 hover:text-blue-700\",\n                                        children: \"Download\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, this),\n            job.logs && job.logs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-medium text-gray-900 mb-3\",\n                        children: \"Activity Log\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 rounded-lg p-4 max-h-64 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: job.logs.map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-400 font-mono text-xs\",\n                                            children: new Date(log.timestamp).toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium \".concat(log.level === 'error' ? 'text-red-400' : log.level === 'warning' ? 'text-yellow-400' : 'text-green-400'),\n                                            children: [\n                                                \"[\",\n                                                log.level.toUpperCase(),\n                                                \"]\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-300\",\n                                            children: log.message\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, log.id, true, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n                lineNumber: 186,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/zmt-cloud/src/components/JobStatus.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n_c = JobStatus;\nvar _c;\n$RefreshReg$(_c, \"JobStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/JobStatus.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/compression-config.ts":
/*!***************************************!*\
  !*** ./src/lib/compression-config.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COMPRESSION_CONFIGS: () => (/* binding */ COMPRESSION_CONFIGS),\n/* harmony export */   estimateCompressionRatio: () => (/* binding */ estimateCompressionRatio),\n/* harmony export */   generateCompressionCommand: () => (/* binding */ generateCompressionCommand),\n/* harmony export */   getCompressionConfig: () => (/* binding */ getCompressionConfig),\n/* harmony export */   getFileExtension: () => (/* binding */ getFileExtension),\n/* harmony export */   getScriptPath: () => (/* binding */ getScriptPath),\n/* harmony export */   getSupportedExtensions: () => (/* binding */ getSupportedExtensions),\n/* harmony export */   isFileSupported: () => (/* binding */ isFileSupported)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// ZMT Compression configuration based on PRD\nconst COMPRESSION_CONFIGS = {\n    // Documents and general files - ZMT compression\n    documents: {\n        extensions: [\n            '.txt',\n            '.pdf',\n            '.xls',\n            '.xlsx',\n            '.doc',\n            '.docx',\n            '.psd',\n            '.csv',\n            '.db',\n            '.dcm',\n            '.ppt',\n            '.pptx'\n        ],\n        script: 'zmt',\n        command: './opt/zmt-scripts/zmt a {outputFile} {inputFile}',\n        description: 'ZMT compression for documents and general files'\n    },\n    // Video files - MP4 compression script\n    video: {\n        extensions: [\n            '.mp4',\n            '.mkv',\n            '.3gp',\n            '.avi',\n            '.mov',\n            '.webm'\n        ],\n        script: 'compress_code_mp4_update.sh',\n        command: './opt/zmt-scripts/compress_code_mp4_update.sh mp4',\n        description: 'Video compression using MP4 optimization'\n    },\n    // Audio files - Audio compression script\n    audio: {\n        extensions: [\n            '.mp3',\n            '.aac',\n            '.opus',\n            '.m4a'\n        ],\n        script: 'compress_code_audio.sh',\n        command: './opt/zmt-scripts/compress_code_audio.sh mp3',\n        description: 'Audio compression optimization'\n    },\n    // Y4M video files - Specialized video compression\n    y4m: {\n        extensions: [\n            '.y4m'\n        ],\n        script: 'compress_code_video.sh',\n        command: './opt/zmt-scripts/compress_code_video.sh',\n        description: 'Y4M video compression'\n    },\n    // TIFF images - Python image compression\n    tiff: {\n        extensions: [\n            '.tif',\n            '.tiff'\n        ],\n        script: 'zmt_image.py',\n        command: 'python3 ./opt/zmt-scripts/zmt_image.py {inputFile}',\n        description: 'TIFF image compression using Python'\n    }\n};\n/**\n * Get compression configuration for a file based on its extension\n */ function getCompressionConfig(filename) {\n    const extension = getFileExtension(filename);\n    for (const config of Object.values(COMPRESSION_CONFIGS)){\n        if (config.extensions.includes(extension)) {\n            return config;\n        }\n    }\n    return null;\n}\n/**\n * Get file extension from filename\n */ function getFileExtension(filename) {\n    const lastDotIndex = filename.lastIndexOf('.');\n    if (lastDotIndex === -1) return '';\n    return filename.substring(lastDotIndex).toLowerCase();\n}\n/**\n * Check if a file type is supported for compression\n */ function isFileSupported(filename) {\n    return getCompressionConfig(filename) !== null;\n}\n/**\n * Get all supported file extensions\n */ function getSupportedExtensions() {\n    const extensions = [];\n    Object.values(COMPRESSION_CONFIGS).forEach((config)=>{\n        extensions.push(...config.extensions);\n    });\n    return [\n        ...new Set(extensions)\n    ].sort();\n}\n/**\n * Generate compression command for a specific file\n */ function generateCompressionCommand(inputFile, outputFile, config) {\n    const path = __webpack_require__(/*! path */ \"(app-pages-browser)/./node_modules/next/dist/compiled/path-browserify/index.js\");\n    const projectRoot = process.cwd();\n    // Convert relative paths to absolute paths\n    let command = config.command;\n    // Replace script paths with absolute paths\n    if (command.startsWith('./opt/zmt-scripts/')) {\n        const scriptName = command.split('./opt/zmt-scripts/')[1].split(' ')[0];\n        const absoluteScriptPath = path.resolve(projectRoot, 'opt', 'zmt-scripts', scriptName);\n        command = command.replace(\"./opt/zmt-scripts/\".concat(scriptName), absoluteScriptPath);\n    }\n    return command.replace('{inputFile}', inputFile).replace('{outputFile}', outputFile);\n}\n/**\n * Get compression script path\n */ function getScriptPath(config) {\n    // In production, these would be absolute paths to the compression scripts\n    return \"/opt/zmt-scripts/\".concat(config.script);\n}\n/**\n * Estimate compression ratio based on file type\n */ function estimateCompressionRatio(filename) {\n    const extension = getFileExtension(filename);\n    // Estimated compression ratios based on file types\n    const ratios = {\n        // Documents - good compression\n        '.txt': 0.3,\n        '.pdf': 0.8,\n        '.doc': 0.6,\n        '.docx': 0.7,\n        '.xls': 0.6,\n        '.xlsx': 0.7,\n        '.csv': 0.4,\n        '.ppt': 0.7,\n        '.pptx': 0.8,\n        // Images - moderate compression\n        '.tif': 0.5,\n        '.tiff': 0.5,\n        '.psd': 0.7,\n        // Video - depends on quality settings\n        '.mp4': 0.6,\n        '.mkv': 0.5,\n        '.avi': 0.4,\n        '.mov': 0.6,\n        '.webm': 0.7,\n        '.3gp': 0.8,\n        '.y4m': 0.3,\n        // Audio - good compression\n        '.mp3': 0.8,\n        '.aac': 0.8,\n        '.opus': 0.8,\n        '.m4a': 0.8,\n        // Database and other\n        '.db': 0.5,\n        '.dcm': 0.6\n    };\n    return ratios[extension] || 0.7; // Default 70% of original size\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/compression-config.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fcardinalvision%2FDocuments%2Faugment-projects%2Fzmt-cloud%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);