<h1 align="center">TypeScript API Utils</h1>

<p align="center">
  Utility functions for working with TypeScript's API.
  Successor to the wonderful tsutils.
  🛠️️
</p>

<p align="center">
	<!-- prettier-ignore-start -->
	<!-- ALL-CONTRIBUTORS-BADGE:START - Do not remove or modify this section -->
<img alt="All Contributors: 10 👪" src="https://img.shields.io/badge/all_contributors-10_👪-21bb42.svg" />
<!-- ALL-CONTRIBUTORS-BADGE:END -->
	<!-- prettier-ignore-end -->
	<a href="https://github.com/JoshuaKGoldberg/ts-api-utils/blob/main/.github/CODE_OF_CONDUCT.md" target="_blank"><img alt="🤝 Code of Conduct: Kept" src="https://img.shields.io/badge/%F0%9F%A4%9D_code_of_conduct-kept-21bb42" /></a>
	<a href="https://codecov.io/gh/JoshuaKGoldberg/ts-api-utils" target="_blank"><img alt="🧪 Coverage" src="https://img.shields.io/codecov/c/github/JoshuaKGoldberg/ts-api-utils?label=%F0%9F%A7%AA%20coverage" /></a>
  <a href="#" target="_blank"><img alt="📚 Documentation Coverage" src="https://raw.githubusercontent.com/JoshuaKGoldberg/ts-api-utils/refs/heads/main/docs/coverage.svg" /></a>
	<a href="https://github.com/JoshuaKGoldberg/ts-api-utils/blob/main/LICENSE.md" target="_blank"><img alt="📝 License: MIT" src="https://img.shields.io/badge/%F0%9F%93%9D_license-MIT-21bb42.svg"></a>
	<a href="http://npmjs.com/package/ts-api-utils"><img alt="📦 npm version" src="https://img.shields.io/npm/v/ts-api-utils?color=21bb42&label=%F0%9F%93%A6%20npm" /></a>
	<img alt="💪 TypeScript: Strict" src="https://img.shields.io/badge/%F0%9F%92%AA_typescript-strict-21bb42.svg" />
</p>

## Usage

```shell
npm i ts-api-utils
```

```ts
import * as tsutils from "ts-api-utils";

tsutils.forEachToken(/* ... */);
```

### API

`ts-api-utils` provides many utility functions.
Check out our API docs for details:

📝 [ts-api-utils API docs](https://joshuakgoldberg.github.io/ts-api-utils).

## Development

See [`.github/CONTRIBUTING.md`](./.github/CONTRIBUTING.md).
Thanks! 💖

## Contributors

Many thanks to [@ajafff](https://github.com/ajafff) for creating the original [`tsutils`](https://github.com/ajafff/tsutils) ([original license: MIT](https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE)) that this project was originally based on! 🙏

<!-- prettier-ignore-start -->
<!-- markdownlint-disable -->
<!-- spellchecker: disable -->
<!-- ALL-CONTRIBUTORS-LIST:START - Do not remove or modify this section -->
<!-- prettier-ignore-start -->
<!-- markdownlint-disable -->
<table>
  <tbody>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://effectivetypescript.com"><img src="https://avatars.githubusercontent.com/u/98301?v=4?s=100" width="100px;" alt="Dan Vanderkam"/><br /><sub><b>Dan Vanderkam</b></sub></a><br /><a href="https://github.com/JoshuaKGoldberg/ts-api-utils/issues?q=author%3Adanvk" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://blog.jmchor.dev"><img src="https://avatars.githubusercontent.com/u/110151013?v=4?s=100" width="100px;" alt="Johannes Chorzempa"/><br /><sub><b>Johannes Chorzempa</b></sub></a><br /><a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=jmchor" title="Documentation">📖</a> <a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=jmchor" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://www.joshuakgoldberg.com"><img src="https://avatars.githubusercontent.com/u/3335181?v=4?s=100" width="100px;" alt="Josh Goldberg"/><br /><sub><b>Josh Goldberg</b></sub></a><br /><a href="https://github.com/JoshuaKGoldberg/ts-api-utils/issues?q=author%3AJoshuaKGoldberg" title="Bug reports">🐛</a> <a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=JoshuaKGoldberg" title="Code">💻</a> <a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=JoshuaKGoldberg" title="Documentation">📖</a> <a href="#projectManagement-JoshuaKGoldberg" title="Project Management">📆</a> <a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=JoshuaKGoldberg" title="Tests">⚠️</a> <a href="#tool-JoshuaKGoldberg" title="Tools">🔧</a> <a href="#maintenance-JoshuaKGoldberg" title="Maintenance">🚧</a> <a href="#infra-JoshuaKGoldberg" title="Infrastructure (Hosting, Build-Tools, etc)">🚇</a> <a href="#ideas-JoshuaKGoldberg" title="Ideas, Planning, & Feedback">🤔</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://twitter.com/kirjs"><img src="https://avatars.githubusercontent.com/u/2545357?v=4?s=100" width="100px;" alt="Kirill Cherkashin"/><br /><sub><b>Kirill Cherkashin</b></sub></a><br /><a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=kirjs" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/kirkwaiblinger"><img src="https://avatars.githubusercontent.com/u/53019676?v=4?s=100" width="100px;" alt="Kirk Waiblinger"/><br /><sub><b>Kirk Waiblinger</b></sub></a><br /><a href="https://github.com/JoshuaKGoldberg/ts-api-utils/issues?q=author%3Akirkwaiblinger" title="Bug reports">🐛</a> <a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=kirkwaiblinger" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/ajafff"><img src="https://avatars.githubusercontent.com/u/11968040?v=4?s=100" width="100px;" alt="Klaus Meinhardt"/><br /><sub><b>Klaus Meinhardt</b></sub></a><br /><a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=ajafff" title="Code">💻</a> <a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=ajafff" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://webpro.nl"><img src="https://avatars.githubusercontent.com/u/456426?v=4?s=100" width="100px;" alt="Lars Kappert"/><br /><sub><b>Lars Kappert</b></sub></a><br /><a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=webpro" title="Code">💻</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/RebeccaStevens"><img src="https://avatars.githubusercontent.com/u/7224206?v=4?s=100" width="100px;" alt="Rebecca Stevens"/><br /><sub><b>Rebecca Stevens</b></sub></a><br /><a href="https://github.com/JoshuaKGoldberg/ts-api-utils/issues?q=author%3ARebeccaStevens" title="Bug reports">🐛</a> <a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=RebeccaStevens" title="Code">💻</a> <a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=RebeccaStevens" title="Documentation">📖</a> <a href="#projectManagement-RebeccaStevens" title="Project Management">📆</a> <a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=RebeccaStevens" title="Tests">⚠️</a> <a href="#tool-RebeccaStevens" title="Tools">🔧</a> <a href="#infra-RebeccaStevens" title="Infrastructure (Hosting, Build-Tools, etc)">🚇</a> <a href="#maintenance-RebeccaStevens" title="Maintenance">🚧</a> <a href="#ideas-RebeccaStevens" title="Ideas, Planning, & Feedback">🤔</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://twitter.com/ronenamiel"><img src="https://avatars.githubusercontent.com/u/5484230?v=4?s=100" width="100px;" alt="Ronen Amiel"/><br /><sub><b>Ronen Amiel</b></sub></a><br /><a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=ronami" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://www.fiskercheung.com/"><img src="https://avatars.githubusercontent.com/u/172584?v=4?s=100" width="100px;" alt="fisker Cheung"/><br /><sub><b>fisker Cheung</b></sub></a><br /><a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=fisker" title="Code">💻</a></td>
    </tr>
  </tbody>
</table>

<!-- markdownlint-restore -->
<!-- prettier-ignore-end -->

<!-- ALL-CONTRIBUTORS-LIST:END -->
<!-- spellchecker: enable -->
<!-- markdownlint-restore -->
<!-- prettier-ignore-end -->

> 💙 This package was templated with [create-typescript-app](https://github.com/JoshuaKGoldberg/create-typescript-app).

> _"My tools! I have to have my tools!" - Dennis Reynolds_
